## 目录说明
本仓库是 moreno repo 结构，主要目录说明如下：

```text
.
├── CONTRIBUTING.md
├── LICENSE
├── README.md
├── dumi               // 站点整体配置、设计等非开发文档
├── packages
│   ├── components     // 主题和基础组件实现，发布为 @otakus/design npm 包
│   ├── staff-selector // 选人组件，发布为 @otakus/staff-selector 包
│   └── ...            // 其他通用组件
├── tsconfig.base.json
└── ...
```

如果你需要新增通用组件，请在 `packages/` 下新增目录，并发布为独立 npm 包供大家使用。

## 参照本仓库创建自己的组件物料仓库

可以使用 create-hoyo 选择 lib 类型即可，文档见 [hoyo monorepo](https://infosys-hoyo.mihoyo.com/guide/monorepo/introduce)。

目前 create-hoyo lib template 还未发到正式版本内，大家需要使用先行版本安装模板：

```shell
npx create-hoyo@1.0.0-alpha.6
```

## 开发环境要求

package.json 中增加了 Node.js 版本要求为 >= [18.19.1](https://nodejs.cn/)，大家在开发组件前请保证自己本地 Node.js 符合要求。

### 为什么要求 Node.js >= 18.19.1

1. 18.19.1 是目前官方的 LTS 版本，较稳定
2. 多人协作开发过程中，如果各自 Node 版本不一样可能会产生一些不必要的问题，如 typescript 类型报错等
3. [CI 配置文件](.gitlab-ci.yml) 中默认使用了 18.19.1 作为 CI 过程中 test/build 等环节的基础镜像设置，开发与 CI 环境一致可以防止出现 “我本地代码 OK 的，但 CI 流程一直挂掉” 的情况

## 代码提交流程

贡献需要以 MR 方式提交，并邀请至少一位仓库成员参与 CR，Merge 操作无法由 MR 发起人操作合并，防止出现自己提自己合的情况。

## 其他开发常见问题

### 我的 VSCode 太老了，打开项目后有些 ts 类型报错

VSCode 有内置安装 TypeScript，有可能会与项目目前安装的版本不一致，某些“类型体操“实现场景下可能会因为内置的版本不一致导致类型报错提示。出现这种情况，请优先尝试重启 VSCode 升级，或者参考 [这里](https://stackoverflow.com/questions/39668731/what-typescript-version-is-visual-studio-code-using-how-to-update-it) 手动调整让 VSCode 使用项目内安装的 TypeScript。