{"name": "lb", "version": "1.0.0", "private": true, "description": "a library template by pnpm, turbo, changeset, vite", "keywords": [], "homepage": "https://platgit.mihoyo.com/ee/infra/otaku-design/otaku-design#readme", "bugs": {"url": "https://platgit.mihoyo.com/ee/infra/otaku-design/otaku-design/issues"}, "repository": {"type": "git", "url": "git+https://platgit.mihoyo.com/ee/infra/otaku-design/otaku-design.git"}, "license": "ISC", "author": "chen.qian", "main": "index.js", "scripts": {"build": "turbo build --filter=./packages/*", "build:ci": "turbo build --filter='./packages/*'", "build:site": "turbo build --filter='./dumi'", "bump": "hoyo-changeset version", "change": "pnpm hoyo-changeset", "ci:eslint": "eslint -f json src -o ./.ci/eslint.json", "clean": "turbo daemon clean", "preinstall": "npx only-allow pnpm", "lint": "eslint --ext .js,mjs,.jsx,.ts,.tsx packages/", "lint:fix": "eslint --fix --ext .js,mjs,.jsx,.ts,.tsx packages/", "prelease": "pnpm hoyo-changeset pre enter", "prelease:exit": "pnpm hoyo-changeset pre exit", "prepare": "husky", "start": "turbo dev --concurrency 30", "test": "jest --coverage"}, "commitlint": {"extends": ["ali"]}, "lint-staged": {"*.{js,mjs,jsx,ts,tsx}": "eslint --fix", "*.{css,less,scss}": "stylelint --fix", "*.{cjs,css,cts,html,js,json,jsx,less,md,mjs,mts,scss,ts,tsx,vue,yaml,yml}": "prettier --write"}, "stylelint": {"extends": ["stylelint-config-ali", "stylelint-prettier/recommended"]}, "devDependencies": {"@babel/core": "7.26.0", "@babel/preset-env": "^7.27.2", "@babel/runtime": "7.26.0", "@changesets/cli": "^2.27.10", "@commitlint/cli": "^19.6.0", "@hoyo/changeset": "^1.0.0", "@jest/globals": "^29.7.0", "@originjs/vite-plugin-commonjs": "^1.0.3", "@otakus/announcement": "0.1.0-beta.6", "@otakus/batch-import": "2.0.0-beta.2", "@otakus/card": "0.3.1", "@otakus/header-config": "0.1.4-beta.10", "@otakus/menu-layout": "0.10.0-beta.9", "@otakus/person-render": "0.2.0-beta.3", "@otakus/rich-text": "0.1.0-beta.1", "@otakus/rich-text-with-at": "0.6.4-beta.7", "@otakus/safe-upload": "1.5.0-beta.8", "@otakus/staff-selector-iam": "0.7.1", "@otakus/sku-selector": "0.1.0-beta.39", "@testing-library/dom": "^8.0.0", "@testing-library/jest-dom": "^5.11.6", "@testing-library/react": "^12.0.0", "@testing-library/user-event": "14.6.1", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.7.9", "babel-jest": "^29.7.0", "commitlint-config-ali": "^1.1.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "less": "^4.2.1", "less-loader": "^11.1.4", "less-plugin-npm-import": "^2.1.0", "lint-staged": "^15.2.10", "only-allow": "^1.2.1", "prettier": "^3.4.2", "stylelint": "^16.11.0", "stylelint-config-ali": "^2.1.1", "stylelint-prettier": "^5.0.2", "ts-jest": "^29.2.5", "turbo": "^2.3.3", "typescript": "^5.0.2", "vite": "^5.2.6", "vite-plugin-commonjs": "^0.10.1", "vite-plugin-css-injected-by-js": "^3.1.0", "vite-plugin-imp": "^2.3.1", "vite-tsconfig-paths": "^4.0.7"}, "packageManager": "pnpm@8.10.2", "engines": {"node": ">=18.19.1", "pnpm": ">=7.0.0"}}