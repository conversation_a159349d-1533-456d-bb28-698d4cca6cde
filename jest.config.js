// jest.config.js
module.exports = {
  // config for mono-repo, see https://jestjs.io/docs/configuration#roots-arraystring
  roots: ["<rootDir>/packages"],
  preset: "ts-jest",
  testEnvironment: "jsdom",
  testMatch: ["**/__tests__/**/*.ts?(x)", "**/?(*.)+(spec|test).ts?(x)"],
  transform: {
    "^.+\\.tsx?$": "ts-jest",
  },
  moduleNameMapper: {
    "\\.(css|less|sass|scss)$": "identity-obj-proxy",
  },
  moduleFileExtensions: ["ts", "tsx", "js", "jsx", "json", "node"],
  // setupFilesAfterEnv: ['@testing-library/jest-dom/extend-expect'],
};
