import { Global, Module } from '@nestjs/common';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { AppConfigService } from './config.service';
import { validateConfig } from './config.schema';

/**
 * 全局配置模块
 */
@Global()
@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      validate: validateConfig,
      validationOptions: {
        allowUnknown: true, // 允许未知的环境变量
        abortEarly: false,  // 显示所有验证错误
      },
    }),
  ],
  providers: [AppConfigService],
  exports: [AppConfigService],
})
export class AppConfigModule {}
