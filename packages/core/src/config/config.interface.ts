/**
 * 应用配置接口
 */
export interface AppConfig {
  // 基础配置
  nodeEnv: 'development' | 'production' | 'test';
  port: number;
  logLevel: 'fatal' | 'error' | 'warn' | 'info' | 'debug' | 'trace';

  // AI 服务配置
  ai: {
    baseUrl: string;
    apiKey: string;
    model: string;
    timeout: number;
  };

  // Figma 配置
  figma: {
    accessToken: string;
    timeout: number;
  };

  // Dify 配置
  dify: {
    baseUrl: string;
    docWorkflowApiKey: string;
    timeout: number;
  };

  // GitLab 配置
  gitlab: {
    personalAccessToken: string;
    rootPath: string;
    timeout: number;
  };

  // 缓存配置
  cache: {
    ttl: number;
    maxItems: number;
    redis?: {
      host: string;
      port: number;
      password?: string;
      db: number;
    };
  };

  // 限流配置
  throttle: {
    short: {
      ttl: number;
      limit: number;
    };
    medium: {
      ttl: number;
      limit: number;
    };
    long: {
      ttl: number;
      limit: number;
    };
  };

  // 健康检查配置
  healthCheck: {
    memory: {
      heapThreshold: number;
      rssThreshold: number;
    };
    disk: {
      threshold: number;
    };
  };

  // 安全配置
  security: {
    corsOrigin: string | string[] | boolean;
  };

  // 监控配置
  monitoring: {
    sentryDsn?: string;
    metricsEnabled: boolean;
  };

  // 文件配置
  file: {
    maxSize: number;
  };

  // 数据库配置
  database?: {
    url: string;
    pool: {
      min: number;
      max: number;
    };
  };
}

/**
 * 环境变量到配置的映射
 */
export const ENV_TO_CONFIG_MAP = {
  // 基础配置
  NODE_ENV: 'nodeEnv',
  PORT: 'port',
  LOG_LEVEL: 'logLevel',

  // AI 服务配置
  AI_BASE_URL: 'ai.baseUrl',
  AI_API_KEY: 'ai.apiKey',
  AI_MODEL: 'ai.model',
  AI_TIMEOUT: 'ai.timeout',

  // Figma 配置
  FIGMA_ACCESS_TOKEN: 'figma.accessToken',
  FIGMA_TIMEOUT: 'figma.timeout',

  // Dify 配置
  DIFY_BASE_URL: 'dify.baseUrl',
  DIFY_DOC_WORKFLOW_API_KEY: 'dify.docWorkflowApiKey',
  DIFY_TIMEOUT: 'dify.timeout',

  // GitLab 配置
  GITLAB_PERSONAL_ACCESS_TOKEN: 'gitlab.personalAccessToken',
  GITLAB_ROOT_PATH: 'gitlab.rootPath',
  GITLAB_TIMEOUT: 'gitlab.timeout',

  // 缓存配置
  CACHE_TTL: 'cache.ttl',
  CACHE_MAX_ITEMS: 'cache.maxItems',
  REDIS_HOST: 'cache.redis.host',
  REDIS_PORT: 'cache.redis.port',
  REDIS_PASSWORD: 'cache.redis.password',
  REDIS_DB: 'cache.redis.db',

  // 限流配置
  THROTTLE_SHORT_TTL: 'throttle.short.ttl',
  THROTTLE_SHORT_LIMIT: 'throttle.short.limit',
  THROTTLE_MEDIUM_TTL: 'throttle.medium.ttl',
  THROTTLE_MEDIUM_LIMIT: 'throttle.medium.limit',
  THROTTLE_LONG_TTL: 'throttle.long.ttl',
  THROTTLE_LONG_LIMIT: 'throttle.long.limit',

  // 健康检查配置
  HEALTH_CHECK_MEMORY_HEAP_THRESHOLD: 'healthCheck.memory.heapThreshold',
  HEALTH_CHECK_MEMORY_RSS_THRESHOLD: 'healthCheck.memory.rssThreshold',
  HEALTH_CHECK_DISK_THRESHOLD: 'healthCheck.disk.threshold',

  // 安全配置
  CORS_ORIGIN: 'security.corsOrigin',

  // 监控配置
  SENTRY_DSN: 'monitoring.sentryDsn',
  METRICS_ENABLED: 'monitoring.metricsEnabled',

  // 文件配置
  MAX_FILE_SIZE: 'file.maxSize',

  // 数据库配置
  DATABASE_URL: 'database.url',
  DATABASE_POOL_MIN: 'database.pool.min',
  DATABASE_POOL_MAX: 'database.pool.max',
} as const;
