/**
 * 应用配置接口
 */
export interface AppConfig {
  // 基础配置
  nodeEnv: 'development' | 'production' | 'test';
  port: number;
  logLevel: 'fatal' | 'error' | 'warn' | 'info' | 'debug' | 'trace';

  // AI 服务配置
  ai: {
    baseUrl: string;
    apiKey: string;
    model: string;
    timeout: number;
  };

  // Figma 配置
  figma: {
    accessToken: string;
    timeout: number;
  };

  // Dify 配置
  dify: {
    baseUrl: string;
    docWorkflowApiKey: string;
    timeout: number;
  };

  // GitLab 配置
  gitlab: {
    personalAccessToken: string;
    rootPath: string;
    timeout: number;
  };

  // 缓存配置
  cache: {
    ttl: number;
    maxItems: number;
    redis?: {
      host: string;
      port: number;
      password?: string;
      db: number;
    };
  };

  // 限流配置
  throttle: {
    short: {
      ttl: number;
      limit: number;
    };
    medium: {
      ttl: number;
      limit: number;
    };
    long: {
      ttl: number;
      limit: number;
    };
  };

  // 健康检查配置
  healthCheck: {
    memory: {
      heapThreshold: number;
      rssThreshold: number;
    };
    disk: {
      threshold: number;
    };
  };

  // 安全配置
  security: {
    corsOrigin: string | string[] | boolean;
  };

  // 监控配置
  monitoring: {
    sentryDsn?: string;
    metricsEnabled: boolean;
  };

  // 文件配置
  file: {
    maxSize: number;
  };

  // 数据库配置
  database?: {
    url: string;
    pool: {
      min: number;
      max: number;
    };
  };
}
