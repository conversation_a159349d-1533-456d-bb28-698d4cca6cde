import * as <PERSON><PERSON> from 'joi';

/**
 * 环境变量验证 Schema
 */
export const configValidationSchema = Joi.object({
  // 基础配置
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test')
    .default('development'),
  
  PORT: Joi.number()
    .port()
    .default(3000),

  // 日志配置
  LOG_LEVEL: Joi.string()
    .valid('fatal', 'error', 'warn', 'info', 'debug', 'trace')
    .default('info'),

  // AI 服务配置
  AI_BASE_URL: Joi.string()
    .uri()
    .required()
    .description('AI 服务基础 URL'),

  AI_API_KEY: Joi.string()
    .required()
    .description('AI 服务 API 密钥'),

  AI_MODEL: Joi.string()
    .default('gpt-4.1-mini')
    .description('AI 模型名称'),

  AI_TIMEOUT: Joi.number()
    .integer()
    .min(1000)
    .max(300000)
    .default(60000)
    .description('AI 服务超时时间（毫秒）'),

  // Figma 配置
  FIGMA_ACCESS_TOKEN: Joi.string()
    .required()
    .description('Figma 访问令牌'),

  FIGMA_TIMEOUT: Joi.number()
    .integer()
    .min(1000)
    .max(120000)
    .default(60000)
    .description('Figma API 超时时间（毫秒）'),

  // Dify 配置
  DIFY_BASE_URL: Joi.string()
    .uri()
    .default('https://api-dify.mihoyo.com/v1')
    .description('Dify 服务基础 URL'),

  DIFY_DOC_WORKFLOW_API_KEY: Joi.string()
    .required()
    .description('Dify 文档工作流 API 密钥'),

  DIFY_TIMEOUT: Joi.number()
    .integer()
    .min(1000)
    .max(600000)
    .default(600000)
    .description('Dify 服务超时时间（毫秒）'),

  // GitLab 配置
  GITLAB_PERSONAL_ACCESS_TOKEN: Joi.string()
    .required()
    .description('GitLab 个人访问令牌'),

  GITLAB_ROOT_PATH: Joi.string()
    .default('repo')
    .description('GitLab 仓库根路径'),

  GITLAB_TIMEOUT: Joi.number()
    .integer()
    .min(1000)
    .max(120000)
    .default(30000)
    .description('GitLab API 超时时间（毫秒）'),

  // 缓存配置
  CACHE_TTL: Joi.number()
    .integer()
    .min(60)
    .default(604800000) // 7 天
    .description('缓存默认 TTL（毫秒）'),

  CACHE_MAX_ITEMS: Joi.number()
    .integer()
    .min(100)
    .default(1000)
    .description('缓存最大项目数'),

  // Redis 配置（可选）
  REDIS_HOST: Joi.string()
    .hostname()
    .optional()
    .description('Redis 主机地址'),

  REDIS_PORT: Joi.number()
    .port()
    .default(6379)
    .when('REDIS_HOST', {
      is: Joi.exist(),
      then: Joi.required(),
    })
    .description('Redis 端口'),

  REDIS_PASSWORD: Joi.string()
    .optional()
    .description('Redis 密码'),

  REDIS_DB: Joi.number()
    .integer()
    .min(0)
    .max(15)
    .default(0)
    .description('Redis 数据库索引'),

  // 限流配置
  THROTTLE_SHORT_TTL: Joi.number()
    .integer()
    .min(100)
    .default(1000)
    .description('短期限流 TTL（毫秒）'),

  THROTTLE_SHORT_LIMIT: Joi.number()
    .integer()
    .min(1)
    .default(10)
    .description('短期限流次数'),

  THROTTLE_MEDIUM_TTL: Joi.number()
    .integer()
    .min(1000)
    .default(10000)
    .description('中期限流 TTL（毫秒）'),

  THROTTLE_MEDIUM_LIMIT: Joi.number()
    .integer()
    .min(1)
    .default(100)
    .description('中期限流次数'),

  THROTTLE_LONG_TTL: Joi.number()
    .integer()
    .min(10000)
    .default(60000)
    .description('长期限流 TTL（毫秒）'),

  THROTTLE_LONG_LIMIT: Joi.number()
    .integer()
    .min(1)
    .default(1000)
    .description('长期限流次数'),

  // 健康检查配置
  HEALTH_CHECK_MEMORY_HEAP_THRESHOLD: Joi.number()
    .integer()
    .min(100 * 1024 * 1024) // 最小 100MB
    .default(300 * 1024 * 1024) // 默认 300MB
    .description('堆内存健康检查阈值（字节）'),

  HEALTH_CHECK_MEMORY_RSS_THRESHOLD: Joi.number()
    .integer()
    .min(100 * 1024 * 1024) // 最小 100MB
    .default(300 * 1024 * 1024) // 默认 300MB
    .description('RSS 内存健康检查阈值（字节）'),

  HEALTH_CHECK_DISK_THRESHOLD: Joi.number()
    .min(0.1)
    .max(0.99)
    .default(0.9)
    .description('磁盘使用率健康检查阈值（百分比）'),

  // 安全配置
  CORS_ORIGIN: Joi.alternatives()
    .try(
      Joi.string().uri(),
      Joi.array().items(Joi.string().uri()),
      Joi.boolean()
    )
    .default(true)
    .description('CORS 允许的源'),

  // 监控配置
  SENTRY_DSN: Joi.string()
    .uri()
    .optional()
    .description('Sentry DSN'),

  METRICS_ENABLED: Joi.boolean()
    .default(false)
    .description('是否启用指标收集'),

  // 文件上传配置
  MAX_FILE_SIZE: Joi.number()
    .integer()
    .min(1024) // 最小 1KB
    .default(10 * 1024 * 1024) // 默认 10MB
    .description('最大文件上传大小（字节）'),

  // 数据库配置（如果使用）
  DATABASE_URL: Joi.string()
    .uri()
    .optional()
    .description('数据库连接 URL'),

  DATABASE_POOL_MIN: Joi.number()
    .integer()
    .min(0)
    .default(2)
    .description('数据库连接池最小连接数'),

  DATABASE_POOL_MAX: Joi.number()
    .integer()
    .min(1)
    .default(10)
    .description('数据库连接池最大连接数'),
});
