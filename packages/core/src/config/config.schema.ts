import { z } from 'zod';

/**
 * 环境变量验证 Schema
 */
export const configValidationSchema = z.object({
  // 基础配置
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.coerce.number().int().min(1).max(65535).default(3000),

  // 日志配置
  LOG_LEVEL: z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),

  // AI 服务配置
  AI_BASE_URL: z.string().url().describe('AI 服务基础 URL'),
  AI_API_KEY: z.string().min(1).describe('AI 服务 API 密钥'),
  AI_MODEL: z.string().default('gpt-4.1-mini').describe('AI 模型名称'),
  AI_TIMEOUT: z.coerce.number().int().min(1000).max(300000).default(60000).describe('AI 服务超时时间（毫秒）'),

  // Figma 配置
  FIGMA_ACCESS_TOKEN: z.string().min(1).describe('Figma 访问令牌'),
  FIGMA_TIMEOUT: z.coerce.number().int().min(1000).max(120000).default(60000).describe('Figma API 超时时间（毫秒）'),

  // Dify 配置
  DIFY_BASE_URL: z.string().url().default('https://api-dify.mihoyo.com/v1').describe('Dify 服务基础 URL'),
  DIFY_DOC_WORKFLOW_API_KEY: z.string().min(1).describe('Dify 文档工作流 API 密钥'),
  DIFY_TIMEOUT: z.coerce.number().int().min(1000).max(600000).default(600000).describe('Dify 服务超时时间（毫秒）'),

  // GitLab 配置
  GITLAB_PERSONAL_ACCESS_TOKEN: z.string().min(1).describe('GitLab 个人访问令牌'),
  GITLAB_ROOT_PATH: z.string().default('repo').describe('GitLab 仓库根路径'),
  GITLAB_TIMEOUT: z.coerce.number().int().min(1000).max(120000).default(30000).describe('GitLab API 超时时间（毫秒）'),

  // 缓存配置
  CACHE_TTL: z.coerce.number().int().min(60).default(604800000).describe('缓存默认 TTL（毫秒）'), // 7 天
  CACHE_MAX_ITEMS: z.coerce.number().int().min(100).default(1000).describe('缓存最大项目数'),

  // Redis 配置（可选）
  REDIS_HOST: z.string().optional().describe('Redis 主机地址'),
  REDIS_PORT: z.coerce.number().int().min(1).max(65535).default(6379).describe('Redis 端口'),
  REDIS_PASSWORD: z.string().optional().describe('Redis 密码'),
  REDIS_DB: z.coerce.number().int().min(0).max(15).default(0).describe('Redis 数据库索引'),

  // 限流配置
  THROTTLE_SHORT_TTL: z.coerce.number().int().min(100).default(1000).describe('短期限流 TTL（毫秒）'),
  THROTTLE_SHORT_LIMIT: z.coerce.number().int().min(1).default(10).describe('短期限流次数'),
  THROTTLE_MEDIUM_TTL: z.coerce.number().int().min(1000).default(10000).describe('中期限流 TTL（毫秒）'),
  THROTTLE_MEDIUM_LIMIT: z.coerce.number().int().min(1).default(100).describe('中期限流次数'),
  THROTTLE_LONG_TTL: z.coerce.number().int().min(10000).default(60000).describe('长期限流 TTL（毫秒）'),
  THROTTLE_LONG_LIMIT: z.coerce.number().int().min(1).default(1000).describe('长期限流次数'),

  // 健康检查配置
  HEALTH_CHECK_MEMORY_HEAP_THRESHOLD: z.coerce.number().int()
    .min(100 * 1024 * 1024) // 最小 100MB
    .default(300 * 1024 * 1024) // 默认 300MB
    .describe('堆内存健康检查阈值（字节）'),
  
  HEALTH_CHECK_MEMORY_RSS_THRESHOLD: z.coerce.number().int()
    .min(100 * 1024 * 1024) // 最小 100MB
    .default(300 * 1024 * 1024) // 默认 300MB
    .describe('RSS 内存健康检查阈值（字节）'),
  
  HEALTH_CHECK_DISK_THRESHOLD: z.coerce.number()
    .min(0.1)
    .max(0.99)
    .default(0.9)
    .describe('磁盘使用率健康检查阈值（百分比）'),

  // 安全配置
  CORS_ORIGIN: z.union([
    z.string().url(),
    z.array(z.string().url()),
    z.boolean(),
    z.literal('*')
  ]).default(true).describe('CORS 允许的源'),

  // 监控配置
  SENTRY_DSN: z.string().url().optional().describe('Sentry DSN'),
  METRICS_ENABLED: z.coerce.boolean().default(false).describe('是否启用指标收集'),

  // 文件上传配置
  MAX_FILE_SIZE: z.coerce.number().int()
    .min(1024) // 最小 1KB
    .default(10 * 1024 * 1024) // 默认 10MB
    .describe('最大文件上传大小（字节）'),

  // 数据库配置（如果使用）
  DATABASE_URL: z.string().url().optional().describe('数据库连接 URL'),
  DATABASE_POOL_MIN: z.coerce.number().int().min(0).default(2).describe('数据库连接池最小连接数'),
  DATABASE_POOL_MAX: z.coerce.number().int().min(1).default(10).describe('数据库连接池最大连接数'),
});

/**
 * 配置类型推断
 */
export type ConfigSchema = z.infer<typeof configValidationSchema>;

/**
 * 验证环境变量
 */
export function validateConfig(env: Record<string, any>): ConfigSchema {
  try {
    return configValidationSchema.parse(env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => 
        `${err.path.join('.')}: ${err.message}`
      ).join('\n');
      
      throw new Error(`配置验证失败:\n${errorMessages}`);
    }
    throw error;
  }
}
