import { Injectable, Logger } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { AppConfig } from './config.interface';
import { ConfigSchema } from './config.schema';

/**
 * 增强的配置服务
 * 提供类型安全的配置访问和验证
 */
@Injectable()
export class AppConfigService {
  private readonly logger = new Logger(AppConfigService.name);
  private readonly config: AppConfig;

  constructor(private readonly nestConfigService: NestConfigService<ConfigSchema>) {
    this.config = this.transformConfig();
    this.logConfigSummary();
  }

  /**
   * 将环境变量配置转换为应用配置
   */
  private transformConfig(): AppConfig {
    const env = this.nestConfigService;

    return {
      // 基础配置
      nodeEnv: env.get('NODE_ENV', { infer: true })!,
      port: env.get('PORT', { infer: true })!,
      logLevel: env.get('LOG_LEVEL', { infer: true })!,

      // AI 服务配置
      ai: {
        baseUrl: env.get('AI_BASE_URL', { infer: true })!,
        apiKey: env.get('AI_API_KEY', { infer: true })!,
        model: env.get('AI_MODEL', { infer: true })!,
        timeout: env.get('AI_TIMEOUT', { infer: true })!,
      },

      // Figma 配置
      figma: {
        accessToken: env.get('FIGMA_ACCESS_TOKEN', { infer: true })!,
        timeout: env.get('FIGMA_TIMEOUT', { infer: true })!,
      },

      // Dify 配置
      dify: {
        baseUrl: env.get('DIFY_BASE_URL', { infer: true })!,
        docWorkflowApiKey: env.get('DIFY_DOC_WORKFLOW_API_KEY', { infer: true })!,
        timeout: env.get('DIFY_TIMEOUT', { infer: true })!,
      },

      // GitLab 配置
      gitlab: {
        personalAccessToken: env.get('GITLAB_PERSONAL_ACCESS_TOKEN', { infer: true })!,
        rootPath: env.get('GITLAB_ROOT_PATH', { infer: true })!,
        timeout: env.get('GITLAB_TIMEOUT', { infer: true })!,
      },

      // 缓存配置
      cache: {
        ttl: env.get('CACHE_TTL', { infer: true })!,
        maxItems: env.get('CACHE_MAX_ITEMS', { infer: true })!,
        redis: this.getRedisConfig(),
      },

      // 限流配置
      throttle: {
        short: {
          ttl: env.get('THROTTLE_SHORT_TTL', { infer: true })!,
          limit: env.get('THROTTLE_SHORT_LIMIT', { infer: true })!,
        },
        medium: {
          ttl: env.get('THROTTLE_MEDIUM_TTL', { infer: true })!,
          limit: env.get('THROTTLE_MEDIUM_LIMIT', { infer: true })!,
        },
        long: {
          ttl: env.get('THROTTLE_LONG_TTL', { infer: true })!,
          limit: env.get('THROTTLE_LONG_LIMIT', { infer: true })!,
        },
      },

      // 健康检查配置
      healthCheck: {
        memory: {
          heapThreshold: env.get('HEALTH_CHECK_MEMORY_HEAP_THRESHOLD', { infer: true })!,
          rssThreshold: env.get('HEALTH_CHECK_MEMORY_RSS_THRESHOLD', { infer: true })!,
        },
        disk: {
          threshold: env.get('HEALTH_CHECK_DISK_THRESHOLD', { infer: true })!,
        },
      },

      // 安全配置
      security: {
        corsOrigin: env.get('CORS_ORIGIN', { infer: true })!,
      },

      // 监控配置
      monitoring: {
        sentryDsn: env.get('SENTRY_DSN', { infer: true }),
        metricsEnabled: env.get('METRICS_ENABLED', { infer: true })!,
      },

      // 文件配置
      file: {
        maxSize: env.get('MAX_FILE_SIZE', { infer: true })!,
      },

      // 数据库配置
      database: this.getDatabaseConfig(),
    };
  }

  /**
   * 获取 Redis 配置
   */
  private getRedisConfig() {
    const host = this.nestConfigService.get('REDIS_HOST', { infer: true });
    if (!host) {
      return undefined;
    }

    return {
      host,
      port: this.nestConfigService.get('REDIS_PORT', { infer: true })!,
      password: this.nestConfigService.get('REDIS_PASSWORD', { infer: true }),
      db: this.nestConfigService.get('REDIS_DB', { infer: true })!,
    };
  }

  /**
   * 获取数据库配置
   */
  private getDatabaseConfig() {
    const url = this.nestConfigService.get('DATABASE_URL', { infer: true });
    if (!url) {
      return undefined;
    }

    return {
      url,
      pool: {
        min: this.nestConfigService.get('DATABASE_POOL_MIN', { infer: true })!,
        max: this.nestConfigService.get('DATABASE_POOL_MAX', { infer: true })!,
      },
    };
  }

  /**
   * 记录配置摘要
   */
  private logConfigSummary() {
    this.logger.log('应用配置加载完成', {
      nodeEnv: this.config.nodeEnv,
      port: this.config.port,
      logLevel: this.config.logLevel,
      hasRedis: !!this.config.cache.redis,
      hasDatabase: !!this.config.database,
      hasSentry: !!this.config.monitoring.sentryDsn,
    });
  }

  /**
   * 获取完整配置
   */
  getConfig(): AppConfig {
    return this.config;
  }

  /**
   * 获取基础配置
   */
  getBaseConfig() {
    return {
      nodeEnv: this.config.nodeEnv,
      port: this.config.port,
      logLevel: this.config.logLevel,
    };
  }

  /**
   * 获取 AI 配置
   */
  getAiConfig() {
    return this.config.ai;
  }

  /**
   * 获取 Figma 配置
   */
  getFigmaConfig() {
    return this.config.figma;
  }

  /**
   * 获取 Dify 配置
   */
  getDifyConfig() {
    return this.config.dify;
  }

  /**
   * 获取 GitLab 配置
   */
  getGitlabConfig() {
    return this.config.gitlab;
  }

  /**
   * 获取缓存配置
   */
  getCacheConfig() {
    return this.config.cache;
  }

  /**
   * 获取限流配置
   */
  getThrottleConfig() {
    return this.config.throttle;
  }

  /**
   * 获取健康检查配置
   */
  getHealthCheckConfig() {
    return this.config.healthCheck;
  }

  /**
   * 获取安全配置
   */
  getSecurityConfig() {
    return this.config.security;
  }

  /**
   * 获取监控配置
   */
  getMonitoringConfig() {
    return this.config.monitoring;
  }

  /**
   * 获取文件配置
   */
  getFileConfig() {
    return this.config.file;
  }

  /**
   * 获取数据库配置
   */
  getDatabaseConfig() {
    return this.config.database;
  }

  /**
   * 判断是否为开发环境
   */
  isDevelopment(): boolean {
    return this.config.nodeEnv === 'development';
  }

  /**
   * 判断是否为生产环境
   */
  isProduction(): boolean {
    return this.config.nodeEnv === 'production';
  }

  /**
   * 判断是否为测试环境
   */
  isTest(): boolean {
    return this.config.nodeEnv === 'test';
  }

  /**
   * 获取原始环境变量值（用于向后兼容）
   */
  get<T = any>(key: keyof ConfigSchema): T | undefined {
    return this.nestConfigService.get(key) as T;
  }

  /**
   * 获取原始环境变量值，如果不存在则返回默认值
   */
  getOrThrow<T = any>(key: keyof ConfigSchema): T {
    return this.nestConfigService.getOrThrow(key) as T;
  }
}
