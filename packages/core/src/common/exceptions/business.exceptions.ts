import { HttpStatus } from '@nestjs/common';
import { BaseException, ErrorCode, ErrorSeverity, ErrorContext } from './base.exception';

/**
 * 验证异常
 */
export class ValidationException extends BaseException {
  constructor(
    message: string = '参数验证失败',
    context: ErrorContext = {},
    cause?: Error,
  ) {
    super(
      message,
      ErrorCode.VALIDATION_ERROR,
      HttpStatus.BAD_REQUEST,
      ErrorSeverity.LOW,
      context,
      false,
      cause,
    );
  }
}

/**
 * 认证异常
 */
export class AuthenticationException extends BaseException {
  constructor(
    message: string = '认证失败',
    context: ErrorContext = {},
    cause?: Error,
  ) {
    super(
      message,
      ErrorCode.AUTHENTICATION_ERROR,
      HttpStatus.UNAUTHORIZED,
      ErrorSeverity.MEDIUM,
      context,
      false,
      cause,
    );
  }
}

/**
 * 授权异常
 */
export class AuthorizationException extends BaseException {
  constructor(
    message: string = '权限不足',
    context: ErrorContext = {},
    cause?: Error,
  ) {
    super(
      message,
      ErrorCode.AUTHORIZATION_ERROR,
      HttpStatus.FORBIDDEN,
      ErrorSeverity.MEDIUM,
      context,
      false,
      cause,
    );
  }
}

/**
 * 资源未找到异常
 */
export class ResourceNotFoundException extends BaseException {
  constructor(
    resource: string,
    identifier?: string,
    context: ErrorContext = {},
    cause?: Error,
  ) {
    const message = identifier 
      ? `${resource} (${identifier}) 未找到`
      : `${resource} 未找到`;
      
    super(
      message,
      ErrorCode.RESOURCE_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      ErrorSeverity.LOW,
      { ...context, resource, identifier },
      false,
      cause,
    );
  }
}

/**
 * 资源已存在异常
 */
export class ResourceAlreadyExistsException extends BaseException {
  constructor(
    resource: string,
    identifier?: string,
    context: ErrorContext = {},
    cause?: Error,
  ) {
    const message = identifier 
      ? `${resource} (${identifier}) 已存在`
      : `${resource} 已存在`;
      
    super(
      message,
      ErrorCode.RESOURCE_ALREADY_EXISTS,
      HttpStatus.CONFLICT,
      ErrorSeverity.LOW,
      { ...context, resource, identifier },
      false,
      cause,
    );
  }
}

/**
 * 资源冲突异常
 */
export class ResourceConflictException extends BaseException {
  constructor(
    message: string = '资源冲突',
    context: ErrorContext = {},
    cause?: Error,
  ) {
    super(
      message,
      ErrorCode.RESOURCE_CONFLICT,
      HttpStatus.CONFLICT,
      ErrorSeverity.MEDIUM,
      context,
      false,
      cause,
    );
  }
}

/**
 * 业务规则违反异常
 */
export class BusinessRuleViolationException extends BaseException {
  constructor(
    rule: string,
    message?: string,
    context: ErrorContext = {},
    cause?: Error,
  ) {
    const errorMessage = message || `违反业务规则: ${rule}`;
    
    super(
      errorMessage,
      ErrorCode.BUSINESS_RULE_VIOLATION,
      HttpStatus.BAD_REQUEST,
      ErrorSeverity.MEDIUM,
      { ...context, rule },
      false,
      cause,
    );
  }
}

/**
 * 限流异常
 */
export class RateLimitException extends BaseException {
  constructor(
    limit: number,
    windowMs: number,
    context: ErrorContext = {},
    cause?: Error,
  ) {
    const message = `请求过于频繁，限制为 ${limit} 次/${windowMs}ms`;
    
    super(
      message,
      ErrorCode.RATE_LIMIT_ERROR,
      HttpStatus.TOO_MANY_REQUESTS,
      ErrorSeverity.LOW,
      { ...context, limit, windowMs },
      true,
      cause,
    );
  }
}

/**
 * 超时异常
 */
export class TimeoutException extends BaseException {
  constructor(
    operation: string,
    timeoutMs: number,
    context: ErrorContext = {},
    cause?: Error,
  ) {
    const message = `操作 ${operation} 超时 (${timeoutMs}ms)`;
    
    super(
      message,
      ErrorCode.TIMEOUT_ERROR,
      HttpStatus.REQUEST_TIMEOUT,
      ErrorSeverity.MEDIUM,
      { ...context, operation, timeoutMs },
      true,
      cause,
    );
  }
}
