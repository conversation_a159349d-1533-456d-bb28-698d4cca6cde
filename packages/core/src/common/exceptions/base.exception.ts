import { HttpException, HttpStatus } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';

/**
 * 错误码枚举
 */
export enum ErrorCode {
  // 通用错误 (1000-1999)
  UNKNOWN_ERROR = 'E1000',
  VALIDATION_ERROR = 'E1001',
  AUTHENTICATION_ERROR = 'E1002',
  AUTHORIZATION_ERROR = 'E1003',
  RATE_LIMIT_ERROR = 'E1004',
  TIMEOUT_ERROR = 'E1005',
  
  // 业务错误 (2000-2999)
  RESOURCE_NOT_FOUND = 'E2000',
  RESOURCE_ALREADY_EXISTS = 'E2001',
  RESOURCE_CONFLICT = 'E2002',
  BUSINESS_RULE_VIOLATION = 'E2003',
  
  // 外部服务错误 (3000-3999)
  EXTERNAL_SERVICE_ERROR = 'E3000',
  FIGMA_API_ERROR = 'E3001',
  DIFY_API_ERROR = 'E3002',
  GITLAB_API_ERROR = 'E3003',
  AI_SERVICE_ERROR = 'E3004',
  
  // 系统错误 (4000-4999)
  DATABASE_ERROR = 'E4000',
  CACHE_ERROR = 'E4001',
  FILE_SYSTEM_ERROR = 'E4002',
  NETWORK_ERROR = 'E4003',
  CONFIGURATION_ERROR = 'E4004',
}

/**
 * 错误严重级别
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * 错误上下文接口
 */
export interface ErrorContext {
  userId?: string;
  requestId?: string;
  correlationId?: string;
  userAgent?: string;
  ip?: string;
  path?: string;
  method?: string;
  timestamp?: Date;
  additionalData?: Record<string, any>;
}

/**
 * 基础异常类
 */
export abstract class BaseException extends HttpException {
  public readonly errorCode: ErrorCode;
  public readonly severity: ErrorSeverity;
  public readonly context: ErrorContext;
  public readonly errorId: string;
  public readonly isRetryable: boolean;
  public readonly cause?: Error;

  constructor(
    message: string,
    errorCode: ErrorCode,
    httpStatus: HttpStatus,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context: ErrorContext = {},
    isRetryable: boolean = false,
    cause?: Error,
  ) {
    super(message, httpStatus);
    
    this.errorCode = errorCode;
    this.severity = severity;
    this.context = {
      ...context,
      timestamp: context.timestamp || new Date(),
    };
    this.errorId = uuidv4();
    this.isRetryable = isRetryable;
    this.cause = cause;
    
    // 设置错误名称
    this.name = this.constructor.name;
    
    // 保持堆栈跟踪
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * 获取错误的详细信息
   */
  getErrorDetails() {
    return {
      errorId: this.errorId,
      errorCode: this.errorCode,
      message: this.message,
      severity: this.severity,
      httpStatus: this.getStatus(),
      isRetryable: this.isRetryable,
      context: this.context,
      timestamp: this.context.timestamp,
      cause: this.cause?.message,
    };
  }

  /**
   * 获取用户友好的错误信息
   */
  getUserFriendlyMessage(): string {
    return this.message;
  }

  /**
   * 判断是否应该记录错误
   */
  shouldLog(): boolean {
    return this.severity !== ErrorSeverity.LOW;
  }

  /**
   * 判断是否应该发送告警
   */
  shouldAlert(): boolean {
    return this.severity === ErrorSeverity.HIGH || this.severity === ErrorSeverity.CRITICAL;
  }

  /**
   * 转换为 JSON 格式
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      errorCode: this.errorCode,
      errorId: this.errorId,
      severity: this.severity,
      httpStatus: this.getStatus(),
      isRetryable: this.isRetryable,
      context: this.context,
      stack: this.stack,
    };
  }
}
