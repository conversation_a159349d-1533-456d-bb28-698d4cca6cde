import { HttpStatus } from '@nestjs/common';
import { BaseException, ErrorCode, ErrorSeverity, ErrorContext } from './base.exception';

/**
 * 外部服务异常基类
 */
export abstract class ExternalServiceException extends BaseException {
  public readonly serviceName: string;
  public readonly serviceUrl?: string;
  public readonly responseStatus?: number;
  public readonly responseData?: any;

  constructor(
    serviceName: string,
    message: string,
    errorCode: ErrorCode,
    httpStatus: HttpStatus = HttpStatus.BAD_GATEWAY,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context: ErrorContext = {},
    isRetryable: boolean = true,
    cause?: Error,
    serviceUrl?: string,
    responseStatus?: number,
    responseData?: any,
  ) {
    super(message, errorCode, httpStatus, severity, context, isRetryable, cause);
    
    this.serviceName = serviceName;
    this.serviceUrl = serviceUrl;
    this.responseStatus = responseStatus;
    this.responseData = responseData;
  }

  getErrorDetails() {
    return {
      ...super.getErrorDetails(),
      serviceName: this.serviceName,
      serviceUrl: this.serviceUrl,
      responseStatus: this.responseStatus,
      responseData: this.responseData,
    };
  }
}

/**
 * Figma API 异常
 */
export class FigmaApiException extends ExternalServiceException {
  constructor(
    message: string = 'Figma API 调用失败',
    context: ErrorContext = {},
    cause?: Error,
    serviceUrl?: string,
    responseStatus?: number,
    responseData?: any,
  ) {
    super(
      'Figma',
      message,
      ErrorCode.FIGMA_API_ERROR,
      HttpStatus.BAD_GATEWAY,
      ErrorSeverity.MEDIUM,
      context,
      true,
      cause,
      serviceUrl,
      responseStatus,
      responseData,
    );
  }

  getUserFriendlyMessage(): string {
    if (this.responseStatus === 401) {
      return 'Figma 访问令牌无效或已过期';
    }
    if (this.responseStatus === 403) {
      return 'Figma 访问权限不足';
    }
    if (this.responseStatus === 404) {
      return 'Figma 文件或资源未找到';
    }
    if (this.responseStatus === 429) {
      return 'Figma API 请求频率过高，请稍后重试';
    }
    return 'Figma 服务暂时不可用，请稍后重试';
  }
}

/**
 * Dify API 异常
 */
export class DifyApiException extends ExternalServiceException {
  constructor(
    message: string = 'Dify API 调用失败',
    context: ErrorContext = {},
    cause?: Error,
    serviceUrl?: string,
    responseStatus?: number,
    responseData?: any,
  ) {
    super(
      'Dify',
      message,
      ErrorCode.DIFY_API_ERROR,
      HttpStatus.BAD_GATEWAY,
      ErrorSeverity.MEDIUM,
      context,
      true,
      cause,
      serviceUrl,
      responseStatus,
      responseData,
    );
  }

  getUserFriendlyMessage(): string {
    if (this.responseStatus === 401) {
      return 'Dify API 密钥无效';
    }
    if (this.responseStatus === 429) {
      return 'Dify API 请求频率过高，请稍后重试';
    }
    return 'AI 服务暂时不可用，请稍后重试';
  }
}

/**
 * GitLab API 异常
 */
export class GitlabApiException extends ExternalServiceException {
  constructor(
    message: string = 'GitLab API 调用失败',
    context: ErrorContext = {},
    cause?: Error,
    serviceUrl?: string,
    responseStatus?: number,
    responseData?: any,
  ) {
    super(
      'GitLab',
      message,
      ErrorCode.GITLAB_API_ERROR,
      HttpStatus.BAD_GATEWAY,
      ErrorSeverity.MEDIUM,
      context,
      true,
      cause,
      serviceUrl,
      responseStatus,
      responseData,
    );
  }

  getUserFriendlyMessage(): string {
    if (this.responseStatus === 401) {
      return 'GitLab 访问令牌无效或已过期';
    }
    if (this.responseStatus === 403) {
      return 'GitLab 访问权限不足';
    }
    if (this.responseStatus === 404) {
      return 'GitLab 仓库或资源未找到';
    }
    return 'GitLab 服务暂时不可用，请稍后重试';
  }
}

/**
 * AI 服务异常
 */
export class AiServiceException extends ExternalServiceException {
  constructor(
    message: string = 'AI 服务调用失败',
    context: ErrorContext = {},
    cause?: Error,
    serviceUrl?: string,
    responseStatus?: number,
    responseData?: any,
  ) {
    super(
      'AI Service',
      message,
      ErrorCode.AI_SERVICE_ERROR,
      HttpStatus.BAD_GATEWAY,
      ErrorSeverity.HIGH,
      context,
      true,
      cause,
      serviceUrl,
      responseStatus,
      responseData,
    );
  }

  getUserFriendlyMessage(): string {
    if (this.responseStatus === 401) {
      return 'AI 服务认证失败';
    }
    if (this.responseStatus === 429) {
      return 'AI 服务请求频率过高，请稍后重试';
    }
    if (this.responseStatus === 503) {
      return 'AI 服务暂时不可用，请稍后重试';
    }
    return 'AI 服务处理失败，请稍后重试';
  }
}

/**
 * 通用外部服务异常
 */
export class GenericExternalServiceException extends ExternalServiceException {
  constructor(
    serviceName: string,
    message: string = '外部服务调用失败',
    context: ErrorContext = {},
    cause?: Error,
    serviceUrl?: string,
    responseStatus?: number,
    responseData?: any,
  ) {
    super(
      serviceName,
      message,
      ErrorCode.EXTERNAL_SERVICE_ERROR,
      HttpStatus.BAD_GATEWAY,
      ErrorSeverity.MEDIUM,
      context,
      true,
      cause,
      serviceUrl,
      responseStatus,
      responseData,
    );
  }

  getUserFriendlyMessage(): string {
    return `${this.serviceName} 服务暂时不可用，请稍后重试`;
  }
}
