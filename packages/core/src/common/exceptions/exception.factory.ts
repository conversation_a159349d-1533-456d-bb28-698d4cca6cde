import { AxiosError } from 'axios';
import { 
  BaseException, 
  ErrorContext,
  ValidationException,
  TimeoutException,
  NetworkException,
  FigmaApiException,
  DifyApiException,
  GitlabApiException,
  AiServiceException,
  GenericExternalServiceException,
  DatabaseException,
  CacheException,
  FileSystemException,
  UnknownSystemException,
} from './index';

/**
 * 异常工厂类
 * 用于根据不同的错误类型创建相应的异常实例
 */
export class ExceptionFactory {
  /**
   * 从 Axios 错误创建异常
   */
  static fromAxiosError(
    error: AxiosError,
    serviceName?: string,
    context: ErrorContext = {},
  ): BaseException {
    const { config, response, code, message } = error;
    const url = config?.url;
    const method = config?.method?.toUpperCase();
    
    const enhancedContext: ErrorContext = {
      ...context,
      url,
      method,
      requestConfig: {
        timeout: config?.timeout,
        headers: config?.headers,
      },
    };

    // 超时错误
    if (code === 'ECONNABORTED' || message.includes('timeout')) {
      return new TimeoutException(
        `${serviceName || 'HTTP'} 请求`,
        config?.timeout || 0,
        enhancedContext,
        error,
      );
    }

    // 网络错误
    if (!response) {
      return new NetworkException(
        `网络请求失败: ${message}`,
        enhancedContext,
        error,
        url,
        method,
        config?.timeout,
      );
    }

    // 根据服务名称创建特定的异常
    const responseStatus = response.status;
    const responseData = response.data;
    const errorMessage = responseData?.message || message;

    switch (serviceName?.toLowerCase()) {
      case 'figma':
        return new FigmaApiException(
          errorMessage,
          enhancedContext,
          error,
          url,
          responseStatus,
          responseData,
        );
      
      case 'dify':
        return new DifyApiException(
          errorMessage,
          enhancedContext,
          error,
          url,
          responseStatus,
          responseData,
        );
      
      case 'gitlab':
        return new GitlabApiException(
          errorMessage,
          enhancedContext,
          error,
          url,
          responseStatus,
          responseData,
        );
      
      case 'ai':
      case 'openai':
        return new AiServiceException(
          errorMessage,
          enhancedContext,
          error,
          url,
          responseStatus,
          responseData,
        );
      
      default:
        return new GenericExternalServiceException(
          serviceName || 'Unknown Service',
          errorMessage,
          enhancedContext,
          error,
          url,
          responseStatus,
          responseData,
        );
    }
  }

  /**
   * 从数据库错误创建异常
   */
  static fromDatabaseError(
    error: Error,
    operation?: string,
    table?: string,
    context: ErrorContext = {},
  ): DatabaseException {
    return new DatabaseException(
      `数据库操作失败: ${error.message}`,
      context,
      error,
      operation,
      table,
    );
  }

  /**
   * 从缓存错误创建异常
   */
  static fromCacheError(
    error: Error,
    cacheKey?: string,
    operation?: string,
    context: ErrorContext = {},
  ): CacheException {
    return new CacheException(
      `缓存操作失败: ${error.message}`,
      context,
      error,
      cacheKey,
      operation,
    );
  }

  /**
   * 从文件系统错误创建异常
   */
  static fromFileSystemError(
    error: Error,
    filePath?: string,
    operation?: string,
    context: ErrorContext = {},
  ): FileSystemException {
    return new FileSystemException(
      `文件系统操作失败: ${error.message}`,
      context,
      error,
      filePath,
      operation,
    );
  }

  /**
   * 从验证错误创建异常
   */
  static fromValidationError(
    error: Error,
    context: ErrorContext = {},
  ): ValidationException {
    return new ValidationException(
      `参数验证失败: ${error.message}`,
      context,
      error,
    );
  }

  /**
   * 从未知错误创建异常
   */
  static fromUnknownError(
    error: unknown,
    context: ErrorContext = {},
  ): BaseException {
    if (error instanceof BaseException) {
      return error;
    }

    if (error instanceof Error) {
      // 检查是否是特定类型的错误
      if (error.name === 'ValidationError') {
        return this.fromValidationError(error, context);
      }
      
      if (error.name === 'TimeoutError') {
        return new TimeoutException(
          '操作超时',
          0,
          context,
          error,
        );
      }

      return new UnknownSystemException(
        error.message,
        context,
        error,
      );
    }

    // 处理非 Error 类型的异常
    return new UnknownSystemException(
      typeof error === 'string' ? error : '未知错误',
      context,
    );
  }

  /**
   * 判断错误是否可重试
   */
  static isRetryableError(error: unknown): boolean {
    if (error instanceof BaseException) {
      return error.isRetryable;
    }

    if (error instanceof AxiosError) {
      // 网络错误通常可重试
      if (!error.response) {
        return true;
      }

      // 5xx 错误通常可重试
      const status = error.response.status;
      return status >= 500 && status < 600;
    }

    return false;
  }

  /**
   * 获取错误的重试延迟时间（毫秒）
   */
  static getRetryDelay(attempt: number, baseDelay: number = 1000): number {
    // 指数退避算法
    return Math.min(baseDelay * Math.pow(2, attempt - 1), 30000);
  }
}
