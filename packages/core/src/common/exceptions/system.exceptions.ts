import { HttpStatus } from '@nestjs/common';
import { BaseException, ErrorCode, ErrorSeverity, ErrorContext } from './base.exception';

/**
 * 数据库异常
 */
export class DatabaseException extends BaseException {
  public readonly operation?: string;
  public readonly table?: string;

  constructor(
    message: string = '数据库操作失败',
    context: ErrorContext = {},
    cause?: Error,
    operation?: string,
    table?: string,
  ) {
    super(
      message,
      ErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      ErrorSeverity.HIGH,
      { ...context, operation, table },
      true,
      cause,
    );
    
    this.operation = operation;
    this.table = table;
  }

  getUserFriendlyMessage(): string {
    return '数据处理失败，请稍后重试';
  }

  getErrorDetails() {
    return {
      ...super.getErrorDetails(),
      operation: this.operation,
      table: this.table,
    };
  }
}

/**
 * 缓存异常
 */
export class CacheException extends BaseException {
  public readonly cacheKey?: string;
  public readonly operation?: string;

  constructor(
    message: string = '缓存操作失败',
    context: ErrorContext = {},
    cause?: Error,
    cacheKey?: string,
    operation?: string,
  ) {
    super(
      message,
      ErrorCode.CACHE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      ErrorSeverity.MEDIUM,
      { ...context, cacheKey, operation },
      true,
      cause,
    );
    
    this.cacheKey = cacheKey;
    this.operation = operation;
  }

  getUserFriendlyMessage(): string {
    return '缓存服务暂时不可用，功能可能会稍慢';
  }

  getErrorDetails() {
    return {
      ...super.getErrorDetails(),
      cacheKey: this.cacheKey,
      operation: this.operation,
    };
  }
}

/**
 * 文件系统异常
 */
export class FileSystemException extends BaseException {
  public readonly filePath?: string;
  public readonly operation?: string;

  constructor(
    message: string = '文件系统操作失败',
    context: ErrorContext = {},
    cause?: Error,
    filePath?: string,
    operation?: string,
  ) {
    super(
      message,
      ErrorCode.FILE_SYSTEM_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      ErrorSeverity.MEDIUM,
      { ...context, filePath, operation },
      true,
      cause,
    );
    
    this.filePath = filePath;
    this.operation = operation;
  }

  getUserFriendlyMessage(): string {
    if (this.operation === 'read') {
      return '文件读取失败';
    }
    if (this.operation === 'write') {
      return '文件写入失败';
    }
    if (this.operation === 'delete') {
      return '文件删除失败';
    }
    return '文件操作失败';
  }

  getErrorDetails() {
    return {
      ...super.getErrorDetails(),
      filePath: this.filePath,
      operation: this.operation,
    };
  }
}

/**
 * 网络异常
 */
export class NetworkException extends BaseException {
  public readonly url?: string;
  public readonly method?: string;
  public readonly timeout?: number;

  constructor(
    message: string = '网络请求失败',
    context: ErrorContext = {},
    cause?: Error,
    url?: string,
    method?: string,
    timeout?: number,
  ) {
    super(
      message,
      ErrorCode.NETWORK_ERROR,
      HttpStatus.BAD_GATEWAY,
      ErrorSeverity.MEDIUM,
      { ...context, url, method, timeout },
      true,
      cause,
    );
    
    this.url = url;
    this.method = method;
    this.timeout = timeout;
  }

  getUserFriendlyMessage(): string {
    if (this.cause?.message?.includes('timeout')) {
      return '网络请求超时，请检查网络连接';
    }
    if (this.cause?.message?.includes('ECONNREFUSED')) {
      return '无法连接到服务器';
    }
    if (this.cause?.message?.includes('ENOTFOUND')) {
      return '服务器地址无法解析';
    }
    return '网络连接失败，请检查网络设置';
  }

  getErrorDetails() {
    return {
      ...super.getErrorDetails(),
      url: this.url,
      method: this.method,
      timeout: this.timeout,
    };
  }
}

/**
 * 配置异常
 */
export class ConfigurationException extends BaseException {
  public readonly configKey?: string;
  public readonly configValue?: any;

  constructor(
    message: string = '配置错误',
    context: ErrorContext = {},
    cause?: Error,
    configKey?: string,
    configValue?: any,
  ) {
    super(
      message,
      ErrorCode.CONFIGURATION_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      ErrorSeverity.CRITICAL,
      { ...context, configKey, configValue },
      false,
      cause,
    );
    
    this.configKey = configKey;
    this.configValue = configValue;
  }

  getUserFriendlyMessage(): string {
    return '系统配置错误，请联系管理员';
  }

  getErrorDetails() {
    return {
      ...super.getErrorDetails(),
      configKey: this.configKey,
      configValue: this.configValue,
    };
  }
}

/**
 * 未知系统异常
 */
export class UnknownSystemException extends BaseException {
  constructor(
    message: string = '系统内部错误',
    context: ErrorContext = {},
    cause?: Error,
  ) {
    super(
      message,
      ErrorCode.UNKNOWN_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      ErrorSeverity.HIGH,
      context,
      false,
      cause,
    );
  }

  getUserFriendlyMessage(): string {
    return '系统暂时不可用，请稍后重试';
  }
}
