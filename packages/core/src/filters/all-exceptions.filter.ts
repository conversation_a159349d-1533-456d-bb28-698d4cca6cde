import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch() // 不带参数，捕获所有未被处理的异常
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // 检查异常是否是 HttpException 的实例
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    const message =
      exception instanceof HttpException
        ? (exception.getResponse() as any).message || exception.message
        : 'Internal server error';

    // 记录错误日志，对于未知错误记录完整的异常对象
    this.logger.error(
      `HTTP Status: ${status} Error: ${message} Path: ${request.url}`,
      exception instanceof Error ? exception.stack : JSON.stringify(exception), // 对于非 Error 实例，序列化为 JSON
    );

    response.status(status).json({
      success: false,
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      message: message,
      // 可以添加一个更通用的错误代码或ID
      error: exception instanceof Error ? exception.name : 'UnknownError',
    });
  }
}
