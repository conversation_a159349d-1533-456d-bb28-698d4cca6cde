import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { BaseException, ExceptionFactory } from '@/common/exceptions';

@Catch() // 不带参数，捕获所有未被处理的异常
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // 构建错误上下文
    const errorContext = {
      userId: (request as any).user?.id,
      requestId: request.headers['x-request-id'] as string,
      correlationId: request.headers['x-correlation-id'] as string,
      userAgent: request.headers['user-agent'],
      ip: request.ip || request.connection.remoteAddress,
      path: request.url,
      method: request.method,
    };

    // 转换为标准异常
    let standardException: BaseException;

    if (exception instanceof BaseException) {
      standardException = exception;
    } else if (exception instanceof HttpException) {
      // 保持原有的 HttpException 处理逻辑
      const status = exception.getStatus();
      const message = (exception.getResponse() as any).message || exception.message;

      standardException = ExceptionFactory.fromUnknownError(
        new Error(message),
        errorContext,
      );
      standardException = Object.assign(standardException, {
        getStatus: () => status
      });
    } else {
      standardException = ExceptionFactory.fromUnknownError(exception, errorContext);
    }

    // 记录错误日志
    this.logException(standardException, request);

    // 发送告警（如果需要）
    if (standardException.shouldAlert()) {
      this.sendAlert(standardException);
    }

    // 构建响应
    const errorResponse = this.buildErrorResponse(standardException, request);

    response.status(standardException.getStatus()).json(errorResponse);
  }

  /**
   * 记录异常日志
   */
  private logException(exception: BaseException, request: Request) {
    if (!exception.shouldLog()) {
      return;
    }

    const logLevel = this.getLogLevel(exception);
    const logMessage = `${exception.errorCode} - ${exception.message}`;
    const logContext = {
      errorId: exception.errorId,
      errorCode: exception.errorCode,
      severity: exception.severity,
      path: request.url,
      method: request.method,
      userAgent: request.headers['user-agent'],
      ip: request.ip,
      stack: exception.stack,
      context: exception.context,
    };

    switch (logLevel) {
      case 'warn':
        this.logger.warn(logMessage, logContext);
        break;
      case 'error':
        this.logger.error(logMessage, logContext);
        break;
      case 'fatal':
        this.logger.fatal(logMessage, logContext);
        break;
      default:
        this.logger.error(logMessage, logContext);
    }
  }

  /**
   * 获取日志级别
   */
  private getLogLevel(exception: BaseException): string {
    switch (exception.severity) {
      case 'low':
        return 'warn';
      case 'medium':
        return 'error';
      case 'high':
        return 'error';
      case 'critical':
        return 'fatal';
      default:
        return 'error';
    }
  }

  /**
   * 发送告警
   */
  private sendAlert(exception: BaseException) {
    // TODO: 实现告警逻辑，例如发送到监控系统
    this.logger.error(`ALERT: Critical error occurred`, {
      errorId: exception.errorId,
      errorCode: exception.errorCode,
      message: exception.message,
      severity: exception.severity,
    });
  }

  /**
   * 构建错误响应
   */
  private buildErrorResponse(exception: BaseException, request: Request) {
    const isDevelopment = process.env.NODE_ENV === 'development';

    const baseResponse = {
      success: false,
      errorId: exception.errorId,
      errorCode: exception.errorCode,
      message: exception.getUserFriendlyMessage(),
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    // 开发环境返回更多调试信息
    if (isDevelopment) {
      return {
        ...baseResponse,
        details: {
          originalMessage: exception.message,
          severity: exception.severity,
          isRetryable: exception.isRetryable,
          stack: exception.stack,
          context: exception.context,
        },
      };
    }

    return baseResponse;
  }
}
