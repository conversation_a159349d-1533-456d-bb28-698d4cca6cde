{"name": "@otakus/ai-server", "version": "0.0.1", "description": "Core package for Otakus AI", "author": "chen.qian", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "format:all": "prettier --write .", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint .", "lint:fix": "eslint . --fix", "prettier": "prettier --write .", "prettier:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@ai-sdk/openai": "^1.3.23", "@modelcontextprotocol/sdk": "1.12.1", "@nestjs/axios": "^4.0.1", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^6.0.0", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "ai": "^4.3.19", "axios": "^1.10.0", "axios-retry": "^4.5.0", "cache-manager": "^7.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "joi": "^17.13.3", "nestjs-pino": "^4.4.0", "nestjs-zod": "^4.3.1", "node-cron": "^4.2.1", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.2.0", "remeda": "^2.26.1", "rxjs": "^7.8.1", "simple-git": "^3.28.0", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@figma/rest-api-spec": "^0.33.0", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"*.{ts,js,json,md}": ["prettier --write"]}}