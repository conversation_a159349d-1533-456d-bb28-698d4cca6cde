import { Domain } from '../type';
import type { SwitchPersonnelCommon } from '../type';
import { getSwitchUserInstance, canSwitchUser, switchUser } from '../utils/switchByNextIAM';

interface UseConfirmProps {
  onConfirm: (params: { token: string; domain: string }) => void;
  closePop?: () => void;
  env: SwitchPersonnelCommon['env'];
}

interface UseConfirmReturn {
  handleConfirm: (domain: Domain[]) => Promise<void>;
}

/**
 * 切换用户身份的钩子函数
 * 根据 isNewIAM 判断使用新版 IAM 还是旧版 IAM
 */
export const useConfirm = ({ onConfirm, closePop, env }: UseConfirmProps): UseConfirmReturn => {
  const handleConfirm = async (domain: Domain[]) => {
    // 检查参数完整性
    const domainValue = domain?.[0]?.domain;
    if (!domainValue) {
      console.warn('切换用户时未获取到有效域账号');
      closePop?.();
      return;
    }

    try {
      // 新版 IAM 切换逻辑
      await handleNewIAMSwitch(domainValue);
    } catch (error) {
      console.error('切换用户过程中发生错误:', error);
    }
  };

  /**
   * 新版 IAM 切换逻辑
   */
  const handleNewIAMSwitch = async (domainValue: string) => {
    // 获取切换实例
    const instance = getSwitchUserInstance(env, domainValue);
    if (!instance) {
      console.warn('无法获取 IAM Next 切换实例');
      return;
    }

    // 检查是否有切换权限
    const canSwitch = await canSwitchUser();
    if (!canSwitch) {
      console.warn('当前用户没有切换权限');
      return;
    }

    // 执行切换
    const switchRes = await switchUser();
    if (switchRes.result) {
      onConfirm({
        token: '',
        domain: domainValue
      });
      closePop?.();
    } else {
      console.warn('切换失败:', switchRes.message);
    }
  };

  return {
    handleConfirm
  };
};
