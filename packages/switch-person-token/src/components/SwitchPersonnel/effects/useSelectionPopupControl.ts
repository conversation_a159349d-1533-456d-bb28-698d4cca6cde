import { ReactElement, cloneElement, useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';

import type { UseSelectionPopupControlParams } from './type';

export const useSelectionPopupControl = ({
  trigger,
  open,
  wrapDom,
  onClose
}: UseSelectionPopupControlParams) => {
  const triggerRef = useRef<ReactElement<any, any>>(null);
  const [selectionPopupStatus, setSelectionPopupStatus] = useState<boolean>(open ?? false);
  const selectionPopupControl = useCallback(
    (isOpen: boolean) => () => {
      setSelectionPopupStatus(isOpen);
      if (!isOpen) {
        onClose?.();
      }
    },
    []
  );
  useEffect(() => {
    if (open) {
      setSelectionPopupStatus(open);
    }
  }, [open]);
  if (open !== undefined || !trigger) {
    triggerRef.current = null;
  } else {
    triggerRef.current = cloneElement(trigger, {
      onClick: selectionPopupControl(true)
    });
  }
  const closePop = () => {
    selectionPopupControl(false)();
    if (wrapDom) {
      ReactDOM.unmountComponentAtNode(wrapDom);
      try {
        wrapDom.parentNode?.removeChild(wrapDom);
      } catch (error) {
        console.warn(error);
      }
    }
  };
  return {
    selectionPopupStatus,
    selectionPopupControl,
    triggerRef,
    closePop
  };
};
