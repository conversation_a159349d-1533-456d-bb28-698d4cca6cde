import { JSX } from 'react';
export interface SwitchPersonnelCommon {
  env: 'test' | 'uat';
  onConfirm?: (params: { token: string; domain: string }) => void;
  language?: 'zh-CN' | 'en-US';
  onClose?: () => void;
  scene: string;
}
export interface SwitchPersonnelProps extends SwitchPersonnelCommon {
  trigger?: JSX.Element;
  open?: boolean;
  wrapDom?: HTMLElement;
}

export interface SwitchPersonnelStaticFreeParams extends SwitchPersonnelCommon {
  container?: HTMLElement;
}

export interface SwitchPersonnelStaticFree {
  (o: SwitchPersonnelStaticFreeParams): void;
}

export interface SwitchPersonnelBase {
  open: SwitchPersonnelStaticFree;
}

export interface Domain {
  domain: string;
  [key: string]: any;
}
