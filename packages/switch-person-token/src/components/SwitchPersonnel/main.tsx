import { OtakuPersonSearch } from '@otakus/staff-selector-iam';
import ReactDOM from 'react-dom';
import { ThemeProvider } from '@otakus/design';
import { useConfirm, useSelectionPopupControl } from './effects';

import type { FC } from 'react';
import type { SwitchPersonnelBase, SwitchPersonnelProps } from './type';

export const SwicthPersonnel: FC<SwitchPersonnelProps> & SwitchPersonnelBase = ({
  trigger,
  open,
  env,
  language,
  onConfirm,
  wrapDom,
  onClose,
  clientId,
  scene
}) => {
  const { selectionPopupStatus, triggerRef, closePop } = useSelectionPopupControl({
    trigger,
    open,
    wrapDom,
    onClose
  });
  const { handleConfirm } = useConfirm({
    env,
    onConfirm,
    closePop
  });
  return (
    <>
      {triggerRef.current}
      <OtakuPersonSearch
        scene={scene}
        clientId={clientId}
        env={env}
        showCustomGroup={false}
        open={selectionPopupStatus}
        onCancel={closePop}
        onConfirm={handleConfirm}
        mode="single"
        language={language || 'zh-CN'}
      />
    </>
  );
};

SwicthPersonnel.open = ({ container, appearance, ...others }) => {
  let wrapDom = container;
  if (!wrapDom) {
    wrapDom = document.createElement('div');
  }
  document.body.appendChild(wrapDom);
  ReactDOM.render(
    <ThemeProvider appearance={appearance}>
      <SwicthPersonnel wrapDom={wrapDom} {...others} open={true} />
    </ThemeProvider>,
    wrapDom
  );
};

// 原来的拼写错误，导出一个正确名称的组件，原有的组件不要动
export const SwitchPersonnel: FC<SwitchPersonnelProps> & SwitchPersonnelBase = SwicthPersonnel;
