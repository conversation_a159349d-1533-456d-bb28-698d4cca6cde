import '@iam/init-sdk/dist/types/types/index.umd.d.ts';
import type { SwitchPersonnelCommon } from '../type';

let instance: iamNext.SwitchUser | null = null;

const getSwitchUserInstance = (
  env: SwitchPersonnelCommon['env'],
  domain: string
): iamNext.SwitchUser | null => {
  // 如果实例已存在，直接返回
  if (instance) {
    return instance;
  }

  // 检查iamNext是否可用
  if (window.iamNext) {
    const clientIdInstance = new window.iamNext.ClientIdManage();
    const clientId = clientIdInstance.getClientId();
    instance = new window.iamNext.SwitchUser(env, clientId, domain);
    return instance;
  }

  console.warn('iamNext SDK未加载，无法创建SwitchUser实例');
  return null;
};

// 检查是否有切人权限
const canSwitchUser = async (): Promise<boolean> => {
  if (!instance) {
    console.warn('SwitchUser实例不存在，无法检查切人权限');
    return false;
  }
  return instance.canSwitchUser();
};

// 切人
const switchUser = async (): Promise<{ result: boolean; message: string }> => {
  if (!instance) {
    console.warn('SwitchUser实例不存在，无法执行切人操作');
    return Promise.reject('SwitchUser实例不存在');
  }
  return instance.switchUser();
};

export { getSwitchUserInstance, canSwitchUser, switchUser };
