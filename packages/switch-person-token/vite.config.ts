import baseConfig from '../../vite.config.base';
import { defineConfig } from 'vite';
import path from 'path';

const entry = path.resolve(__dirname, './src');
import { devDependencies, peerDependencies } from './package.json';

export default defineConfig({
  ...baseConfig,
  define: {
    'process.env.NODE_ENV': '"production"'
  },
  esbuild: {
    drop: ['debugger']
  },
  resolve: {
    alias: {
      '@otakus/switch-personnel-cookie': path.resolve(__dirname, './src/index.ts')
    }
  },
  build: {
    cssCodeSplit: true,
    target: 'es2015',
    lib: {
      entry
    },
    rollupOptions: {
      external: (id) => {
        return (
          Object.keys(peerDependencies).includes(id) ||
          Object.keys(devDependencies).includes(id) ||
          id.includes('antd')
        );
      },
      output: [
        {
          format: 'es',
          entryFileNames: '[name].js',
          preserveModules: true,
          preserveModulesRoot: './src',
          dir: 'es'
        },
        {
          format: 'cjs',
          entryFileNames: '[name].js',
          dir: 'cjs'
        },
        {
          format: 'umd',
          entryFileNames: '[name].js',
          dir: 'umd',
          name: 'OtakusSwitchPersonToken',
          globals: {
            react: 'React',
            'react-dom': 'ReactDOM',
            lodash: 'lodash',
            'lodash-es': 'lodash-es',
            dayjs: 'dayjs',
            antd: 'antd',
            'antd-style': 'antdStyle',
            '@otakus/staff-selector-iam': 'OtakusStaffSelector',
            '@otakus/design': 'OtakusDesign'
          }
        }
      ]
    }
  }
});
