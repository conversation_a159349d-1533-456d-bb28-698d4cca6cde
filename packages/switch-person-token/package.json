{"name": "@otakus/switch-personnel-cookie", "version": "2.1.0", "description": "", "keywords": [], "license": "ISC", "author": "", "main": "cjs/index.js", "module": "es/index.js", "types": "es/index.d.ts", "files": ["cjs", "es", "umd", "CHANGELOG.md", "README.md"], "scripts": {"build": "vite build && tsc", "dev": "vite build --watch --emptyOutDir=false"}, "peerDependencies": {"@otakus/staff-selector-iam": ">=0.6.0", "react": ">=16.9.0", "react-dom": ">=16.9.0"}, "devDependencies": {"@iam/init-sdk": "0.1.0-test.132", "@otakus/design": "workspace:^", "@otakus/staff-selector-iam": "^0.6.5", "react": "^17.0.2", "react-dom": "^17.0.2"}, "publishConfig": {"registry": "https://npm.mihoyo.com/"}}