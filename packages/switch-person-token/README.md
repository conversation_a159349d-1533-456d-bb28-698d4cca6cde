---
title: 切换账号
group:
  title: 安全合规
  order: 1
order: 4
isLogin: true
---

> 切人组件，只在 test 和 uat 环境生效哦，选人能力依赖了新版选人组件，所以需要安装新版选人～

## 安装

```bash
pnpm add @otakus/staff-selector-iam @otakus/switch-personnel-cookie
```

<code src="./demo/basic.tsx">基础用法</code>

### BaseParams（共用的参数）

> API 调用和组件调用共用的参数

| 参数      |                        类型                         | 是否必传 |  默认值 |                                                                备注 |
| :-------- | :-------------------------------------------------: | -------: | ------: | ------------------------------------------------------------------: |
| env       |                   'test' \| 'uat'                   |       是 |       - |                                                            对应环境 |
| scene     |                       string                        |       是 |       - | 业务场景id，声明该场景的数据选择范围，和clientId绑定，需要向IAM申请 |
| language  |                 "zh-CN" \| "en-US"                  |       否 | 'zh-CN' |                                                                语言 |
| onClose   |                     () => void                      |       否 |       - |                                  它常常和 open 一起使用，受控模式下 |
| onConfirm | (params: { token: string; domain: string }) => void |       否 |       - |                                                      确认的回调函数 |

### openParams(open 方法调用的参数)

> 在 BaseParams 的基础上，额外支持以下参数

| 参数      |    类型     | 是否必传 |    默认值 |                                      备注 |
| :-------- | :---------: | -------: | --------: | ----------------------------------------: |
| container | HTMLElement |       否 | root 节点 | 弹窗挂在位置，默认在 react 的 root 节点下 |

### Props(组件参数)

> 在 BaseParams 的基础上，额外支持以下参数

| 参数    |    类型     | 是否必传 | 默认值 |                              备注 |
| :------ | :---------: | -------: | -----: | --------------------------------: |
| trigger | JSX.Element |       否 |      - |    trigger 模式，使用起来比较简单 |
| open    |   boolean   |       否 |      - | 受控模式，常常与 onClose 一起使用 |

<embed src="./CHANGELOG.md"></embed>
