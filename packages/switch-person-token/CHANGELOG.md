## 更新日志

## 2.1.0

`2025-06-26`

- 🐞 修复UMD打包问题
- 🆕 新增参数clientId，允许外部指定clientId

## 2.0.0

`2025-05-22`

- 🛠 选人组件peerDependency 依赖替换为新版选人组件@otakus/staff-selector-iam
- 🛠 移除 `isNewIAM`、`refreshLocalToken`、`handleRequestError` 属性
- 🛠 `onConfirm` 回调中的 token 字段将不再返回admin-token，始终为空
- 🆕 新增 `scene` 属性

## 1.2.0

`2025-05-15`

- 🆕 isNewIAM为true时，使用`@iam/init-sdk`提供的切人能力

## 1.1.0

`2025-01-19`

- 🆕 新增 isNewIAM 参数，支持新版 IAM 服务
- 🆕 去除 @otakus/design peerDependency 依赖

## 1.0.0

`2024-12-10`

- 🆕 底层依赖由 antd 调整为 @otakus/design 与较新版本的选人组件
