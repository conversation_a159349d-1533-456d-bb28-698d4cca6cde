import { Button, Card } from '@otakus/design';
import { SwicthPersonnel } from '@otakus/switch-personnel-cookie';
import Tip from './demo-tip';
import React, { useState } from 'react';

const App: React.FC = () => {
  const [showSPModal, setShowSpModal] = useState(false);
  return (
    <>
      {HOYO_ENV === 'test' ? (
        <div>
          <h3>切人弹窗组件，提供了三种方式，分别为api模式、受控模式、trigger模式的使用。</h3>
          <Card title="使用方式1:API模式">
            <>
              <h4>API模式，调用比较方便，您可以直接通过.open来实现它。</h4>
              <Button
                type="primary"
                onClick={() => {
                  SwicthPersonnel.open({
                    env: 'test',
                    scene: 'OTAKU_TEST_DOMAIN_ENABLE',
                    onConfirm: ({ token, domain }) => {
                      // 也可以使用onConfirm来定制您的业务逻辑
                      console.log('token为空');
                      console.log({ token, domain });
                      setTimeout(() => {
                        window.location.reload();
                      }, 0);
                    }
                  });
                }}
              >
                任意dom
              </Button>
            </>
          </Card>
          <Card title="使用方式2:trigger模式">
            <>
              <h4>trigger模式，也是非受控模式。</h4>
              <SwicthPersonnel
                env="test"
                scene="OTAKU_TEST_DOMAIN_ENABLE"
                onConfirm={({ token, domain }) => {
                  console.log('token为空');
                  console.log({ token, domain });
                  setTimeout(() => {
                    window.location.reload();
                  }, 0);
                }}
                trigger={
                  <Button
                    type="primary"
                    onClick={() => {
                      setShowSpModal(true);
                    }}
                  >
                    任意dom
                  </Button>
                }
              />
            </>
          </Card>
          <Card title="使用方式3:受控模式">
            <h4>受控模式，需要open和onClose来进行受控。</h4>
            <>
              <Button
                type="primary"
                onClick={() => {
                  setShowSpModal(true);
                }}
              >
                任意dom
              </Button>
              <SwicthPersonnel
                env="test"
                scene="OTAKU_TEST_DOMAIN_ENABLE"
                open={showSPModal}
                onClose={() => {
                  setShowSpModal(false);
                }}
                onConfirm={({ token, domain }) => {
                  console.log('token为空');
                  console.log({ token, domain });
                  setTimeout(() => {
                    window.location.reload();
                  }, 0);
                }}
              />
            </>
          </Card>
        </div>
      ) : (
        <Tip />
      )}
    </>
  );
};

export default App;
