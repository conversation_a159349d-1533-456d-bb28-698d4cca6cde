## 0.7.1

`2025-06-19`

- 🐞 修复动态切换isReadOnly时组件报`Rendered fewer hooks than expected`的问题

## 0.7.0

`2025-06-18`

- 账号回显接口替换，支持在途账号停用后回显
- 失效组织增加只读态 + 失效标签展示
- 组织只读态文字颜色适配暗黑

## 0.6.11

`2025-06-19`

- 🐞 修复动态切换isReadOnly时组件报`Rendered fewer hooks than expected`的问题

## 0.6.9

`2025-06-13`

- 🐞 修复personSearch从近期选择群组中进入后重新打开弹窗时没有展示从群组选择的问题
- 优化单选时输入框左内边距
- 优化下拉框中option向前省略的样式
- 增加埋点信息上报

## 0.6.7

`2025-05-30`

- 单选时交互优化
- 🆕 增加skipDataFetch参数，可配置选账号时是否使用用户传入的数据进行回显。具体用法参考api说明
- 获取scene config的接口，增加sessionStorage缓存

## 0.6.6

`2025-05-22`

- 🆕 选账号时支持选择自定义数据
- 🐞 修复单个组件快速切换多个scene时的竞态问题
- 🐞 优化行政组织头像为mihoyo租户头像

## 0.6.5

`2025-05-07`

- 🐞 修复如果在onchange中将数据format成domain list赋值给组件后select可以重复选择同一个账号的问题

## 0.6.4

`2025-04-28`

- 🐞 调整群组图标大小（20px->26px）
- 🆕 新增suffixVisible参数，控制是否展示打开选人弹窗的icon

## 0.6.3

`2025-04-23`

- 🆕 给vue版本开放口子，支持配置theme

## 0.6.2

`2025-04-16`

- onConfirm方法增加return false不关闭弹窗功能
- 调整react依赖版本为^17.0.0

## 0.6.1

`2025-04-02`

- 🐞 单选时不展示全选按钮
- 🐞 当`showCustomGroup`设置为false时，过滤近期选择和搜索结果中的群组数据
- 🐞 自定义服务OpenService中参数clientId修改为非必传

## 0.6.0

`2025-03-26`

- 🆕 新增自定义群组功能
  - 仅在选账号场景中可使用（purpose = account）
  - 新增`showCustomGroup`属性，用于自定义群组展示，默认为true。
- 🆕 选账号场景中（purpose = account），删除组织下钻入口，禁用下钻能力
- 🆕 搜索结果增加高亮展示。
- 🆕 只读态&数据为空时增加'-'兜底。
- 🆕 新增自定义服务对象（OpenService）
  - 支持复写组件请求函数
  - 支持新增自定义用户组
- ⚠️ 删除`showOrg`属性。
- ⚠️ 移除账号+组织选择场景，如有需求，请联系IAM以及Otakus。
- ⚠️ 目前，尚未同步UC自定义群组数据，如需保留原有的群组数据，请联系IAM。

## 0.5.0

`2025-03-18`

- 🐞 修复批量添加问题
- 🆕 新增 ErrorBoundary 来捕获组件内部错误并上报到 Sentry
- 🆕 新增 onError API，允许业务方监听组件内部错误

## 0.4.2

`2025-03-07`

- 🐞 修复单选时，checkbox不能反选的问题
- 🐞 修复单纯选账号场景行政组织下钻问题

## 0.3.5

`2025-02-25`

- 🆕 disabled为true时，样式调整，并阻止唤起弹框

## 0.3.4

`2025-02-20`

- 🆕 回显时不展示domain
- 🐞 组织只读态没有展示全路径的问题
- 🐞 混选模式增加accountkey和departmentkey参数

## 0.3.3

`2025-02-11`

- 🐞 修复maxTagCount的popover中tag样式问题
- 🐞 修复同一页面中多处使用组件后配置不同scene没有正确处理的问题

## 0.3.1

## 0.3.2

`2025-02-11`

- 🐞 修复personSearch 搜索后再打开面板loading的问题
- 🐞 修复清空表单后有一条接口提示的问题

## 0.3.0

`2025-02-07`

- 🆕 只读态（PersonRender）升级到0.3.0, 支持wave跳转
- 🆕 新增参数：readOnlyProps，透传参数给只读态（PersonRender）
- 🐞 修复未使用themeProvider时，组件样式异常问题
