import { OtakuPersonSelect } from '@otakus/staff-selector-iam';
import React, { useState } from 'react';

export default function Example() {
  // 下拉框选人列表
  const [value, setValue] = useState(['meng.li02']);

  return (
    <>
      <div style={{ width: 300 }}>
        {/* select 选账号 */}
        <OtakuPersonSelect
          value={value}
          env={HOYO_ENV}
          purpose="account"
          scene={OTAKU_SCENE_DOMAIN_ENABLE}
          showOrg={false}
          onChange={(val) => {
            console.log(val);
            setValue(val);
          }}
          maxTagCount={20}
          maxTagTextLength={20}
        />
      </div>
    </>
  );
}
