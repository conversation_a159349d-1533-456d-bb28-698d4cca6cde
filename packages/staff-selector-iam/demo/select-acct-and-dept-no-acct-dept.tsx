import { OtakuPersonSelect } from '@otakus/staff-selector-iam';
import React, { useState } from 'react';

export default function Example() {
  // 下拉框选人列表
  const [value, setValue] = useState([
    {
      value: 73050,
      isUser: true
    }
  ]);

  return (
    <>
      <div style={{ width: 300 }}>
        {/* select 选组织 */}
        <OtakuPersonSelect
          value={value}
          showCommon={false}
          showOrg={false}
          env={HOYO_ENV}
          scene={OTAKU_SCENE}
          purpose="hybrid"
          onChange={(val) => {
            console.log(val);
            setValue(val);
          }}
        />
      </div>
    </>
  );
}
