import { OtakuPersonSelect } from '@otakus/staff-selector-iam';
import React, { useState } from 'react';

export default function Example() {
  // 下拉框选人列表
  const [value, setValue] = useState([3750]);

  return (
    <>
      <div style={{ width: 300 }}>
        {/* select 选组织 */}
        <OtakuPersonSelect
          value={value}
          env={HOYO_ENV}
          showOrg={false}
          scene={OTAKU_SCENE_ORG}
          purpose="department"
          onChange={(val) => {
            console.log(val);
            setValue(val);
          }}
        />
      </div>
    </>
  );
}
