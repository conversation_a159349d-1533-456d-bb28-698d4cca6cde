import { OtakuPersonSelect } from '@otakus/staff-selector-iam';
import React, { useState } from 'react';
import Tip from './demo-tip';

export default function Example() {
  // 下拉框选人列表
  const [value, setValue] = useState(['2552']);

  return (
    <>
      <div style={{ width: 300 }}>
        {/* select 选组织 */}
        {HOYO_ENV === 'test' ? (
          <OtakuPersonSelect
            value={value}
            env={HOYO_ENV}
            scene={OTAKU_SCENE_HR}
            departmentKey={'org_code'}
            purpose="department"
            onChange={(val) => {
              console.log(val);
              setValue(val);
            }}
          />
        ) : (
          <Tip />
        )}
      </div>
    </>
  );
}
