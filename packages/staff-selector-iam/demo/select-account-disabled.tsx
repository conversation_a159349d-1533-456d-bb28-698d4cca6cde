import { OtakuPersonSelect } from '@otakus/staff-selector-iam';
import React, { useState } from 'react';

export default function Example() {
  // 下拉框选人列表
  const [value, setValue] = useState([73050]);

  return (
    <>
      <div style={{ width: 300 }}>
        {/* select 选账号 */}
        <OtakuPersonSelect
          value={value}
          env={HOYO_ENV}
          scene={OTAKU_SCENE}
          disabled
          purpose="account"
          onChange={(val) => {
            console.log(val);
            setValue(val);
          }}
          maxTagCount={20}
        />
      </div>
    </>
  );
}
