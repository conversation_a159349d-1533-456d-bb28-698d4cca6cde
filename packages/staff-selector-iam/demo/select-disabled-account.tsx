import { OtakuPersonSelect } from '@otakus/staff-selector-iam';
import React, { useState } from 'react';
import Tip from './demo-tip';

export default function Example() {
  // 下拉框选人列表
  const [value, setValue] = useState(['hewired.test']);

  return (
    <>
      <div style={{ width: 300 }}>
        {/* select 选账号 */}
        {HOYO_ENV === 'test' ? (
          <OtakuPersonSelect
            value={value}
            env={HOYO_ENV}
            scene={OTAKU_SCENE_DOMAIN}
            accountKey={'domain'}
            purpose="account"
            onChange={(val) => {
              console.log(val);
              setValue(val);
            }}
            maxTagCount={20}
          />
        ) : (
          <Tip />
        )}
      </div>
    </>
  );
}
