import {
  OtakuPersonSearch,
  IAccount,
  IDepartment,
  IGroup,
  PurposeType,
  HoYoEnv,
  AccountPrimaryKeyType,
  DepartmentPrimaryKeyType,
  ICustomServiceItem,
  OpenService,
  CustomService
} from '@otakus/staff-selector-iam';
import React, { useState, useMemo } from 'react';
import { ThemeProvider, Button } from '@otakus/design';

class MyCustomService extends CustomService<IAccount, IDepartment> {
  getConfig() {
    // 如果需要作为根节点展示在下钻入口, 需要设置showRootList: true
    return {
      purpose: 'department',
      key: 'my-custom-service',
      title: '自定义部门',
      rootBreadcrumbTitle: '自定义部门',
      tabTitle: '自定义部门',
      icon: ''
    };
  }
  async getRootList(): Promise<ICustomServiceItem<IAccount, IDepartment>[]> {
    return await Promise.resolve([
      {
        id: '1',
        type: 'custom',
        isLeaf: false,
        title: '部门',
        subTitle: '用户组A',
        avatar: 'https://img.yzcdn.cn/vant/ipad.png'
      }
    ]);
  }
  async getHierarchy(
    item: ICustomServiceItem<IAccount, IDepartment>
  ): Promise<ICustomServiceItem<IAccount, IDepartment>[]> {
    console.log('正在准备下钻的Item', item);
    return await Promise.resolve([
      {
        id: '1117',
        type: 'department',
        avatar:
          'https://hoyowave-ugc-testing.mihoyo.com/***************/app-avatar-a5cf562a-3c0c-4eee-a10d-1c20b47847b6.png',
        title: '企业工程与效能组'
      }
    ]);
  }
  async getDetail() {
    return await Promise.resolve([
      {
        id: '1118',
        type: 'department',
        avatar:
          'https://hoyowave-ugc-testing.mihoyo.com/***************/app-avatar-a5cf562a-3c0c-4eee-a10d-1c20b47847b6.png',
        title: '企业工程与效能组',
        isLeaf: true
      }
    ]);
  }
  async search(keyword: string) {
    console.log('正在搜索', keyword);
    return await Promise.resolve([
      {
        id: '1117',
        avatar:
          'https://hoyowave-ugc-testing.mihoyo.com/***************/app-avatar-a5cf562a-3c0c-4eee-a10d-1c20b47847b6.png',
        title: '企业工程与效能组',
        isLeaf: true
      },
      {
        id: '1118',
        avatar:
          'https://hoyowave-ugc-testing.mihoyo.com/*************/3fad5cb8712bd2ef4a11721a0880b94048f7ac18ab8d42450041cd9526928cc3.png',
        title: '企业工程与效能组',
        isLeaf: true
      }
    ]);
  }
}

class MyCustomService2 extends CustomService<IAccount, IDepartment> {
  getConfig() {
    return {
      purpose: 'account',
      key: 'my-custom-service-2',
      title: '我的用户组2',
      rootBreadcrumbTitle: '我的用户组2',
      tabTitle: '我的用户组2',
      showRootList: true, // 控制是否作为根节点展示在下钻入口
      icon: 'https://hoyowave-ugc-testing.mihoyo.com/*************/3fad5cb8712bd2ef4a11721a0880b94048f7ac18ab8d42450041cd9526928cc3.png'
    };
  }
  async getRootList(): Promise<ICustomServiceItem<IAccount, IDepartment>[]> {
    return await Promise.resolve([
      {
        id: '1',
        type: 'custom',
        isLeaf: false,
        title: '用户组A',
        subTitle: '用户组A',
        avatar:
          'https://hoyowave-ugc-testing.mihoyo.com/*************/3fad5cb8712bd2ef4a11721a0880b94048f7ac18ab8d42450041cd9526928cc3.png',
        selectable: false // 当前自定义数据是否可选
      },
      {
        id: '2',
        type: 'custom',
        isLeaf: false,
        title: '用户组B',
        subTitle: '用户组B',
        avatar:
          'https://hoyowave-ugc-testing.mihoyo.com/***************/app-avatar-********-c2b1-4880-bc55-4848e7888bd0.png',
        selectable: false // 当前自定义数据是否可选
      }
    ]);
  }
  async getHierarchy(
    item: ICustomServiceItem<IAccount, IDepartment>
  ): Promise<ICustomServiceItem<IAccount, IDepartment>[]> {
    console.log('正在准备下钻的Item', item);
    return await Promise.resolve([
      {
        id: '1',
        type: 'account',
        isLeaf: true,
        primary_key: 'meng.li02',
        primary_key_status: 1,
        mi_id: 73050,
        user_name: '李蒙',
        cn_name: '李蒙',
        en_name: 'Meng.L',
        domain: 'meng.li02',
        avatar_url:
          'https://hoyowave-ugc-testing.mihoyo.com/***************/b5f91a4caeaa706d06c39e9f93f28c7c52c439e5c4a50a7e196987e31bb39f1f.jpg',
        email: '<EMAIL>',
        emp_no: '06248',
        tenant_id: 1,
        status: 1,
        user_type: 1
      }
    ]);
  }
  async search(keyword: string) {
    console.log('正在搜索', keyword);
    return await Promise.resolve([
      {
        id: '1',
        type: 'account',
        isLeaf: true,
        primary_key: 'meng.li02',
        primary_key_status: 1,
        mi_id: 73050,
        user_name: '李蒙',
        cn_name: '李蒙',
        en_name: 'Meng.L',
        domain: 'meng.li02',
        avatar_url:
          'https://hoyowave-ugc-testing.mihoyo.com/***************/b5f91a4caeaa706d06c39e9f93f28c7c52c439e5c4a50a7e196987e31bb39f1f.jpg',
        email: '<EMAIL>',
        emp_no: '06248',
        tenant_id: 1,
        status: 1,
        user_type: 1
      }
    ]);
  }
}

class MyOpenService extends OpenService<IAccount, IDepartment, IGroup> {
  constructor(params: {
    purpose: PurposeType;
    scene: string;
    clientId: string;
    env: HoYoEnv;
    accountKey: AccountPrimaryKeyType;
    departmentKey?: DepartmentPrimaryKeyType;
  }) {
    super(params);
  }
  registerCustomService() {
    return [new MyCustomService(), new MyCustomService2()];
  }
  // 重写转换账号数据
  async transformAccount(account: IAccount[]): Promise<IAccount[]> {
    return Promise.resolve(
      account.map((item) => {
        return {
          ...item,
          disabled: item.domain === 'feixiang.he'
        };
      })
    );
  }
}

export default function Example() {
  const [value, setValue] = useState([
    {
      value: 'meng.li02',
      isUser: true
    },
    {
      value: '1118',
      isCustom: 'my-custom-service' // 和自定义服务中getConfig返回的key保持一致，决定回显调用的自定义服务是哪一个
    }
  ]);

  // 使用 useMemo 缓存 openService
  const openService = useMemo(
    () =>
      new MyOpenService({
        purpose: 'account',
        scene: OTAKU_SCENE_DOMAIN_ENABLE,
        clientId: OtakusConfig?.clientId,
        env: HOYO_ENV,
        accountKey: 'domain'
      }),
    []
  );
  const [dialogOpen, setDialogOpen] = useState(false);

  return (
    <ThemeProvider>
      <div style={{ width: 300 }}>
        <Button onClick={() => setDialogOpen(true)}>打开选人组件</Button>
        {/* select 选账号 */}
        <OtakuPersonSearch
          open={dialogOpen}
          value={value}
          env={HOYO_ENV}
          purpose="account"
          scene={OTAKU_SCENE_DOMAIN_ENABLE}
          language="zh-CN"
          showCommon
          limit={5}
          onCancel={() => setDialogOpen(false)}
          openService={openService}
          onConfirm={(val) => {
            console.log('onConfirm', val);

            setValue(val);
            setDialogOpen(false);
          }}
        />
      </div>
    </ThemeProvider>
  );
}
