import { OtakuPersonSelect } from '@otakus/staff-selector-iam';
import React, { useState } from 'react';

export default function Example() {
  // 下拉框选人列表
  const [value1, setValue1] = useState([]);
  // const [value2, setValue2] = useState([]);
  // const [value3, setValue3] = useState([]);
  return (
    <>
      <div style={{ width: 300 }}>
        选人，限制10个：
        <OtakuPersonSelect
          value={value1}
          env={HOYO_ENV}
          scene={OTAKU_SCENE}
          limit={10}
          purpose="account"
          onChange={(val) => {
            setValue1(val);
          }}
        />
      </div>
      {/* <div style={{ width: 300 }}>
        选组，限制5个：
        <OtakuPersonSelect
          value={value2}
          env={HOYO_ENV}
          scene={OTAKU_SCENE}
          deptLimit={5}
          purpose="department"
          onChange={(val) => {
            setValue2(val);
          }}
        />
      </div>
      <div style={{ width: 300 }}>
        混合，人限制10个，组限制5个：
        <OtakuPersonSelect
          value={value3}
          env={HOYO_ENV}
          scene={OTAKU_SCENE}
          limit={10}
          deptLimit={5}
          purpose="hybrid"
          onChange={(val) => {
            setValue3(val);
          }}
        />
      </div> */}
    </>
  );
}
