import { OtakuPersonSelect } from '@otakus/staff-selector-iam';
import React, { useState } from 'react';

export default function Example() {
  // 下拉框选人列表
  const [value, _] = useState([{ cn_name: '李蒙', domain: 'meng.li02' }]);

  return (
    <>
      <div style={{ width: 300 }}>
        {Array.from({ length: 1 }).map(() => {
          return (
            <OtakuPersonSelect
              value={value}
              env={HOYO_ENV}
              scene={OTAKU_SCENE_DOMAIN_ENABLE}
              purpose="account"
              skipDataFetch={true}
              onChange={(val) => {
                console.log(1111111, val);
                // setValue(val.map(item => item.domain));
              }}
            />
          );
        })}
      </div>
    </>
  );
}
