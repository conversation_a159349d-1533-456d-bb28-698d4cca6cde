{"name": "@otakus/staff-selector-iam-docs", "version": "0.3.0", "description": "选人组件", "keywords": [], "homepage": "https://unpkg.com/@otakus/staff-selector@1.1.2/build/index.html", "license": "ISC", "author": "", "main": "cjs/index.js", "module": "es/index.js", "types": "es/index.d.ts", "files": ["cjs", "es", "dist", "umd", "CHANGELOG.md"], "deployFiles": ["dist", "umd"], "scripts": {"build": "echo 'No need to build'"}, "dependencies": {"axios": "^1.6.1", "react-window": "^1.8.10"}, "devDependencies": {"@babel/core": "7.26.0", "@babel/runtime": "7.26.0", "@alilc/lowcode-react-renderer": "^1.2.5", "@alilc/lowcode-types": "^1.2.5", "@alilc/lowcode-utils": "^1.2.5", "@apaas-core/designer-sdk": "^1.0.4", "@otakus/design": "workspace:*", "@otakus/icons": "^9.1.0", "@testing-library/jest-dom": "^5.16.5", "@types/jest": "^29.5.12", "react": "~17.0.2", "react-dom": "~17.0.2", "rollup-plugin-visualizer": "^5.12.0"}, "peerDependencies": {"@otakus/design": ">=0.8.0", "@otakus/icons": ">=9.1.0", "react": ">=16.9.0", "react-dom": ">=16.9.0"}, "publishConfig": {"registry": "https://npm.mihoyo.com/"}}