import { Empty, Modal, Popover, Select, TreeSelect } from '@otakus/design';
import { NewWindowOutlined } from '@otakus/icons';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  ICurrentSelectItem,
  IOrgItem,
  IOtakuPersonSearchProps,
  IOtakuPersonSelectProps,
  IUserItem
} from '../../types';
import OtakuPersonSearch from '../person-search';
import { useApi } from '../person-search/api';
import Avatar from '../person-search/components/Avatar';
import PersonSearchSVG from '../person-search/components/PersonSearchSVG';
import { debounce, isGroup, isNonEmptyObject, isStringNumberArray } from '../person-search/utils';
import OptionContainer from './components/OptionContainer';
import OrganizationContainer from './components/OrganizationContainer';
import ReadOnly from './components/ReadOnly';
import { usePrefixCls } from '../../hooks/usePrefixCls';

// import './index.less';
import { PURPOSE_ENUM, REPORT_SELECT_CHANNEL_ENUM } from '../../constants/enum';
import {
  formatData,
  formatOrgData,
  formatUserData,
  formatUserOrgData
} from '../../utils/formatData';
import { isEqualList } from '../../utils/isEqualList';
import { isOldDeptData, isOldUserData } from '../../utils/oldData';
import { locale } from '../person-search/locale';
import { useStyles } from './style';

// interface TreeItemProps {
//   dept_id: number;
//   cn_name: string;
//   en_name: string;
//   value: any;
// }

const TagRenderer = ({ label, purposeType, item, onClose, onCloseTag, tagValue }) => {
  const prefix = usePrefixCls();
  const { styles } = useStyles({ prefixCls: prefix });
  const [optionPopover, setOptionPopover] = useState(false);
  const labelRef = useRef(null);
  const onOpenChange = (value) => {
    if (onShowTooltip()) return;
    setOptionPopover(value);
  };

  const onShowTooltip = () => {
    const labelEl = labelRef.current;
    const isLabelOverflow = labelEl && labelEl.scrollWidth > labelEl.clientWidth;
    return !isLabelOverflow;
  };

  const popoverContent = (
    <div className={styles.otakusStaffSelectOptionPopover}>
      <span className={styles.otakusSelectTreeItemOptionContent}>{label}</span>
    </div>
  );

  return (
    <Popover
      content={popoverContent}
      trigger="hover"
      placement="top"
      destroyTooltipOnHide
      mouseEnterDelay={0.3}
      zIndex={9999}
      align={{ offset: [0, -4] }}
      onOpenChange={onOpenChange}
      open={optionPopover}
    >
      <div
        className={`${prefix}-select-selection-item ${styles.otakusTagSelectionItem} ${styles.otakusStaffSelectTagContainer}`}
      >
        <Avatar
          size={20}
          purpose={purposeType}
          icon={item?.tenant_id === 1 ? 'mihoyoDeptAvatar' : 'hoyoverseDeptAvatar'}
          avatarUrl={item?.avatar_url}
          styles={{ marginRight: '4px' }}
        />

        <span
          className={`${styles.otakusSelectSelectionItemContent} ${prefix}-select-selection-item-content`}
          ref={labelRef}
        >
          {label}
        </span>
        {/* {isUserResigned && (
    <div className="otakus-select-selection-item--resigned">
      <span>{i18n('is_resigned', '已离职')}</span>
    </div>
  )} */}
        <span
          className={`${prefix}-select-selection-item-remove ${styles.otakusSelectTagRenderClose}`}
          onClick={
            onClose ||
            (() => {
              onCloseTag(tagValue);
            })
          }
          style={{ display: 'inline' }}
        >
          <PersonSearchSVG type="tagClose" />
        </span>
      </div>
    </Popover>
  );
};

const { SHOW_ALL } = TreeSelect;

const PersonSearch = (props: IOtakuPersonSearchProps) => {
  const {
    value,
    open,
    onConfirm,
    onCancel,
    // handleOptionsChange,
    language,
    purpose
  } = props;

  const handleCancel = () => {
    onCancel?.();
  };

  const handleConfirm = (selectedList) => {
    const selectedValue = selectedList?.filter((i) => !isGroup(i));
    onCancel?.();

    const formatDataInfo = selectedValue ? formatData(selectedValue, { language, purpose }) : [];
    // 设置 select 的值
    onConfirm?.(formatDataInfo as any);
  };

  return (
    open && (
      <OtakuPersonSearch
        {...props}
        open={open}
        value={value}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        onError={props.onError}
      />
    )
  );
};

const PersonSelect = (props: IOtakuPersonSelectProps) => {
  // 默认多选、labelInValue
  const {
    allowClear = true,
    mode = 'multiple',
    labelInValue = true,
    value: propsValue,
    env,
    headers,
    limit,
    deptLimit = limit,
    isReadOnly = false,
    clientId,
    maxCount,
    purpose = PURPOSE_ENUM.ACCOUNT,
    language = 'zh-CN',
    localeFile,
    showCommon = true,
    accountKey,
    departmentKey,
    scene
  } = props;
  const prefix = usePrefixCls();
  const { styles } = useStyles({ prefixCls: prefix });

  const [searchValue, setSearchValue] = useState('');
  // 展示选人组件
  const [open, setOpen] = useState(false);
  // 选人组件默认值
  const [value, setValue] = useState([]);
  // 下拉框的 value
  const [selectValue, setSelectValue] = useState<
    { item: IUserItem | IOrgItem; value: number; label: string; purpose: PURPOSE_ENUM }[]
  >([]);
  // 记录上一次的 propsValue
  const prevSelectValueRef = useRef([]);

  const [hasChanged, setHasChanged] = useState(false);

  const [options, setOptions] = useState<
    {
      item: IUserItem | IOrgItem;
      value: number;
      label: string;
      purpose: PURPOSE_ENUM;
      isLeaf?: boolean;
    }[]
  >([]);

  const fetchRef = useRef(0);

  const [selectOpen, setSelectOpen] = useState(false);
  const [optionsPopover, setOptionsPopover] = useState(false);

  const [loading, setLoading] = useState(false);

  // 具有最近选择
  const [hasRecentSelect, setHasRecentSelect] = useState(false);

  // 具有最近选择
  const [recentData, setRecentData] = useState([]);

  // 上报选择记录
  const reportSelectLog = useRef(new Map());

  const recordPopover = useRef(new Map());
  const [popupScroll, setPopupScroll] = useState(false);

  // 选人上限弹窗
  const [errorOpen, setErrorOpen] = useState(false);

  const {
    getAcctDetail,
    searchUserByIam,
    searchDeptsByIam,
    searchUserDeptsByIam,
    getDeptHierarchy,
    getRootDeptDetail,
    getListDeptProfile,
    updateReportCurrentSelect,
    getFetchRecentSelect
  } = useApi({
    env,
    headers,
    clientId,
    scene
  });

  const finalMode = mode || 'multiple';

  const finalLanguage = language || 'zh-CN';
  const finalLocale = {
    'en-US': { ...locale['en-US'], ...(localeFile?.['en-US'] || {}) },
    'zh-CN': { ...locale['zh-CN'], ...(localeFile?.['zh-CN'] || {}) }
  };

  const finalMaxCount = useMemo(() => {
    return finalMode === 'multiple' ? maxCount : 2;
  }, [finalMode, maxCount]);

  const notFoundContentInfo = useMemo(() => {
    let info = {
      iconType: 'searchKeyword',
      text: finalLocale[finalLanguage]?.please_search_keywords
    };
    if (searchValue) {
      info = {
        iconType: 'searchEmpty',
        text: finalLocale[finalLanguage]?.search_results_empty
      };
    }
    if (loading) {
      info = {
        iconType: 'searchLoading',
        text: finalLocale[finalLanguage]?.Loading
      };
    }

    return info;
  }, [searchValue, loading]);

  // 多选时展示Checkbox
  const treeCheckable = useMemo(() => {
    return finalMode !== 'single';
  }, [finalMode]);

  // 初始化
  useEffect(() => {
    let deptOptions: any[] = [];
    // 获取最近选择
    const fetchRecentSelect = async () => {
      if (!showCommon) {
        // 不获取近期选择数据
        return;
      }
      let { accts = [], depts = [] } = await getFetchRecentSelect();

      if (purpose === PURPOSE_ENUM.ACCOUNT) {
        depts = [];
      } else if (purpose === PURPOSE_ENUM.DEPARTMENT) {
        accts = [];
      }

      deptOptions = formatData([...accts, ...depts], {
        language,
        reportSelectType: REPORT_SELECT_CHANNEL_ENUM.RECENT,
        purpose
      }) as any[];

      if (deptOptions) {
        setHasRecentSelect(true);
      }
      setOptions(deptOptions);
      setRecentData(deptOptions);
    };
    // 获取组织
    const fetchRootOrgData = async () => {
      const depts = await getRootDeptDetail();

      if (depts && depts.length > 0) {
        const requests = depts.map((item) =>
          getDeptHierarchy({
            deptId: item.dept_id,
            showChildDept: true,
            showChildMi: false
          })
        );
        const results = await Promise.all(requests);
        deptOptions = results.reduce((el, option) => {
          if (Array.isArray(option)) {
            el.push(
              ...(formatOrgData(option, language, REPORT_SELECT_CHANNEL_ENUM.DEPT) as IOrgItem[])
            );
          }
          return el;
        }, []);
        setOptions(deptOptions || []);
      }
    };

    fetchRecentSelect().finally(() => {
      if (
        purpose === PURPOSE_ENUM.DEPARTMENT &&
        Array.isArray(deptOptions) &&
        deptOptions.length === 0
      ) {
        fetchRootOrgData();
      }
    });
  }, []);

  const defaultAccount = (tempValue) => {
    // 如果value是number[],或者是旧接口数据，则调接口获取回显值
    if (isStringNumberArray(tempValue) || isOldUserData(tempValue)) {
      getAcctDetail(tempValue, accountKey)
        .then((res) => {
          setSelectValue(res ? formatUserData(res, language) : []);
        })
        .catch((err) => {
          console.log('==err', err);
        });
    } else if (
      !prevSelectValueRef.current ||
      !isEqualList(tempValue, prevSelectValueRef.current, purpose)
    ) {
      // value为object[], 直接回填
      setSelectValue(propsValue ? formatUserData(propsValue as any, language) : []);
      prevSelectValueRef.current = propsValue;
    }
  };

  const defaultDept = (tempValue) => {
    // 如果value是number[],或者是旧接口数据, 则调接口获取回显值
    if (isStringNumberArray(tempValue) || isOldDeptData(tempValue)) {
      getListDeptProfile(tempValue, departmentKey)
        .then((res) => {
          setSelectValue(res ? formatOrgData(res, language) : []);
        })
        .catch((err) => {
          console.log('==err', err);
        });
    } else if (
      !prevSelectValueRef.current ||
      !isEqualList(tempValue, prevSelectValueRef.current, purpose)
    ) {
      // value为object[], 直接回填
      setSelectValue(propsValue ? formatOrgData(propsValue as any, language) : []);
      prevSelectValueRef.current = propsValue;
    }
  };

  const defaultAccountAndDept = async (tempValue) => {
    const [valueData] = tempValue;

    if (!isNonEmptyObject(valueData)) {
      setSelectValue([]);
      return;
    }

    const valueDataKeys = Object.keys(valueData);
    if (
      valueDataKeys.length === 2 &&
      valueDataKeys.every((key) => ['value', 'isDept', 'isUser'].includes(key))
    ) {
      // const params = tempValue.reduce((el, item) => {}, );
      const paramsAccts = [];
      const paramsDepts = [];
      const indexMap = new Map();
      tempValue.forEach((item, index) => {
        indexMap.set(item.value, index);
        if (item?.isUser) {
          paramsAccts.push(item.value);
        } else {
          paramsDepts.push(item.value);
        }
      });
      const results = [];
      if (paramsAccts.length) {
        results.push(getAcctDetail(paramsAccts));
      }
      if (paramsDepts.length) {
        results.push(getListDeptProfile(paramsDepts));
      }
      const data = await Promise.all(results);

      // 根据原数据排序
      const newTempValue = (formatUserOrgData(data.flat(), language) as any[])?.sort((a, b) => {
        const indexA = indexMap.get(a.value) !== undefined ? indexMap.get(a.value) : Infinity;
        const indexB = indexMap.get(b.value) !== undefined ? indexMap.get(b.value) : Infinity;
        return indexA - indexB;
      });

      setSelectValue(newTempValue);
    } else if (
      !prevSelectValueRef.current ||
      !isEqualList(tempValue, prevSelectValueRef.current, purpose)
    ) {
      setSelectValue(propsValue ? formatUserOrgData(propsValue as any, language) : []);
      prevSelectValueRef.current = propsValue;
    }
  };
  // 设置默认值
  useEffect(() => {
    // 空值处理
    if (!propsValue) {
      setSelectValue([]);
      return;
    }

    const tempValue = Array.isArray(propsValue) ? propsValue : [propsValue];

    switch (purpose) {
      case PURPOSE_ENUM.ACCOUNT:
        defaultAccount(tempValue);
        break;
      case PURPOSE_ENUM.DEPARTMENT:
        defaultDept(tempValue);
        break;
      case PURPOSE_ENUM.HYBRID:
        defaultAccountAndDept(tempValue);
        break;
    }
  }, [propsValue]);

  // 触发 onChange
  useEffect(() => {
    const selectedList = Array.isArray(selectValue) ? selectValue.map((i) => i.item) : [];
    // selectedList 为 [] 时也触发
    if (
      !isEqualList(selectedList, prevSelectValueRef.current, purpose) ||
      (selectedList.length === 0 && hasChanged)
    ) {
      if (typeof props?.onChange === 'function') {
        props.onChange(selectedList);
        setHasChanged(false);
        prevSelectValueRef.current = selectedList;
      }
    }
  }, [selectValue, hasChanged]);

  // 点击右侧 icon
  const handleClick = () => {
    const selectedList = Array.isArray(selectValue) ? selectValue.map((i) => i.item) : [];

    setValue(selectedList);
    // 打开选人弹窗
    setOpen(true);
  };

  // 下拉框选择
  const handleChange = (option) => {
    if (option) {
      const optionList = Array.isArray(option) ? option : [option];
      const allOptionValue = [...selectValue, ...options];
      let newValue = [];

      newValue = optionList
        .map((item) => {
          return allOptionValue.find((i) => i.value === item.value);
        })
        .filter(Boolean);

      if (purpose === PURPOSE_ENUM.ACCOUNT) {
        if (newValue.length > limit) {
          newValue = newValue.slice(0, limit);
          setErrorOpen(true);
        }
      } else if (purpose === PURPOSE_ENUM.DEPARTMENT) {
        if (newValue.length > deptLimit) {
          newValue = newValue.slice(0, deptLimit);
          setErrorOpen(true);
        }
      } else if (purpose === PURPOSE_ENUM.HYBRID) {
        const total = newValue.reduce(
          (el, item) => {
            if (item.purpose === PURPOSE_ENUM.ACCOUNT) {
              el.account += 1;
            } else if (item.purpose === PURPOSE_ENUM.DEPARTMENT) {
              el.dept += 1;
            }
            return el;
          },
          {
            account: 0,
            dept: 0
          }
        );
        if (total.account > limit || total.dept > deptLimit) {
          newValue = newValue.slice(0, newValue.length - 1);
          setErrorOpen(true);
        }
      }

      // 单选只保留最后一个值
      if (finalMode === 'single' && newValue.length > 1) {
        newValue = newValue.slice(-1);
      }

      setSelectValue(newValue);
    } else {
      setSelectValue([]);
    }
    setHasChanged(true);
  };

  // 下拉框清除
  const handleClear = () => {
    setOptions(recentData || []);
    setSearchValue('');
    setSelectValue([]);
    reportSelectLog.current.clear();
  };

  // 弹窗值 change
  const handleSelectValueChange = (val) => {
    setSelectValue(val);
    setHasChanged(!isEqualList(val, prevSelectValueRef.current, purpose));
  };

  const handleBlur = () => {
    setSelectOpen(false);
  };

  // 搜索人
  const loadAccountOptions = (keyword: string) => {
    const fetchId = fetchRef.current;
    searchUserByIam(keyword)
      .then((newOptions: any) => {
        if (fetchId !== fetchRef.current) {
          return;
        }
        // 把完整的人员信息绑定在item字段，回显
        setOptions(
          newOptions ? formatUserData(newOptions, language, REPORT_SELECT_CHANNEL_ENUM.SEARCH) : []
        );
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 搜索组织
  const loadDeptOptions = (keyword: string) => {
    const fetchId = fetchRef.current;
    searchDeptsByIam(keyword)
      .then((newOptions: any) => {
        if (fetchId !== fetchRef.current) {
          return;
        }

        // 把完整的组织信息绑定在item字段，回显
        const formattedOptions = formatOrgData(
          newOptions,
          language,
          REPORT_SELECT_CHANNEL_ENUM.SEARCH
        );
        setOptions(formattedOptions);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 搜索人和组织
  const loadAccountOrgOptions = (keyword: string) => {
    const fetchId = fetchRef.current;

    searchUserDeptsByIam(keyword)
      .then((newOptions: any) => {
        if (fetchId !== fetchRef.current) {
          return;
        }

        // 把完整的人员信息绑定在item字段，回显
        setOptions(
          newOptions
            ? formatUserOrgData(newOptions, language, REPORT_SELECT_CHANNEL_ENUM.SEARCH)
            : []
        );
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const loadOptions = (keyword: string) => {
    keyword = keyword.trim();
    if (keyword === '') {
      return;
    }
    setLoading(true);
    setSearchValue(keyword);
    fetchRef.current += 1;

    switch (purpose) {
      case PURPOSE_ENUM.ACCOUNT:
        loadAccountOptions(keyword);
        break;
      case PURPOSE_ENUM.DEPARTMENT:
        loadDeptOptions(keyword);
        break;
      case PURPOSE_ENUM.HYBRID:
        loadAccountOrgOptions(keyword);
        break;
    }
  };

  // 在组件中
  const debounceFetcher = debounce(loadOptions, 300);

  const SuffixIcon = () => (
    <div onClick={handleClick} className={`${prefix}-staff-select-suffix`}>
      <NewWindowOutlined />
    </div>
  );

  const onCloseTag = (val) => {
    if (Array.isArray(selectValue)) {
      const selectList = selectValue.filter((item) => item.value !== val);
      setSelectValue(selectList);
      setHasChanged(true);
    }
  };

  const onRecordPopover = (option) => {
    recordPopover.current.set(option.value, option);
  };

  const tagRender = (tagProps) => {
    const { label, isMaxTag, value: tagValue, onClose } = tagProps;
    const item = Array.isArray(selectValue)
      ? selectValue.find((option) => option.value === tagValue)?.item
      : undefined;

    const purposeType = item?.isDept
      ? PURPOSE_ENUM.DEPARTMENT
      : item?.isUser
        ? PURPOSE_ENUM.ACCOUNT
        : '';

    // const isUserResigned = isResigned(item);
    if (isMaxTag) {
      return <div className={`${styles.otakusTagSelectionItem} is-max-tag`}>{label}</div>;
    }

    return (
      <TagRenderer
        label={label}
        purposeType={purposeType}
        item={item}
        onClose={onClose}
        onCloseTag={onCloseTag}
        tagValue={tagValue}
      />
    );
  };

  const optionRender = (optionProps) => {
    const { key, ...optionData } = optionProps;
    return purpose === PURPOSE_ENUM.DEPARTMENT ? (
      <OrganizationContainer
        {...optionData}
        key={optionData.value}
        searchValue={searchValue}
        onOpen={setOptionsPopover}
        language={language}
      />
    ) : (
      <OptionContainer
        {...optionData}
        key={optionData.value}
        popupScroll={popupScroll}
        recordPopover={recordPopover.current}
        onOpen={setOptionsPopover}
        onRecordPopover={onRecordPopover}
        language={language}
      />
    );
  };

  const maxTagPlaceholder = (omittedValues) => {
    const content = (
      <div className={`${prefix}-select-selection-overflow ${styles.staffMaxPopover}`}>
        {omittedValues.map((item) => (
          <div className={`${prefix}-select-selection-overflow-item`} key={item.value}>
            {tagRender(item)}
          </div>
        ))}
      </div>
    );

    return omittedValues.length ? (
      <Popover
        placement="top"
        overlayClassName={`${styles.otakusStaffSelectPopover} ${prefix}-select-outlined ${prefix}-select-multiple`}
        content={content}
      >
        <span>+ {omittedValues.length}</span>
      </Popover>
    ) : (
      <></>
    );
  };

  const notFoundContent = (
    <div
      className={`${styles.otakusSelectItemEmptyContent} ${prefix}-select-item-empty-content ${loading ? 'is-loading' : ''}`}
    >
      {notFoundContentInfo.iconType === 'searchEmpty' ? (
        <Empty
          imageStyle={{ height: 60 }}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={notFoundContentInfo.text}
        />
      ) : notFoundContentInfo.iconType === 'searchKeyword' ? (
        <Empty imageStyle={{ height: 60 }} description={notFoundContentInfo.text} />
      ) : (
        <>
          <PersonSearchSVG type={notFoundContentInfo.iconType} />
          <span>{notFoundContentInfo.text}</span>
        </>
      )}
    </div>
  );

  // 下拉框选中-记录当前选择
  const handleSelect = (_, option) => {
    const { value: optionValue, item } = option;
    const { reportSelectType } = item;

    const selectLogData: ICurrentSelectItem = {
      channel: reportSelectType,
      item_id: optionValue
    };

    if (reportSelectType === REPORT_SELECT_CHANNEL_ENUM.DEPT) {
      selectLogData.src_id = item.parent_dept_id;
    }

    if (reportSelectType === REPORT_SELECT_CHANNEL_ENUM.RECENT) {
      selectLogData.src_id = item.record_id;
    }

    if (reportSelectType === REPORT_SELECT_CHANNEL_ENUM.SEARCH) {
      selectLogData.src_str = searchValue;
    }

    reportSelectLog.current.set(optionValue, selectLogData);
  };

  // 上报当前选择
  const reportRecord = async () => {
    if (!Array.isArray(selectValue) || !selectValue.length) return;
    let isReport = false;
    const params = {
      accts: [],
      depts: []
    };

    selectValue.forEach((item) => {
      const logItem = reportSelectLog.current.get(item.value);

      if (logItem) {
        const paramsKey = purpose === PURPOSE_ENUM.ACCOUNT ? 'accts' : 'depts';
        params[paramsKey].push(logItem);
        if (!isReport) {
          isReport = true;
        }
      }
    });

    if (isReport) {
      await updateReportCurrentSelect(params);
      reportSelectLog.current.clear();
    }
  };

  const closePopupScroll = debounce(() => {
    setPopupScroll(false);
  }, 300);

  const handlePopupScroll = () => {
    if (!popupScroll) {
      setPopupScroll(true);
    }
    closePopupScroll();
  };

  const onDropdownVisibleChange = (nextOpen) => {
    if (optionsPopover && !nextOpen) {
      return;
    }

    setSelectOpen(nextOpen);

    // 面板收起，上报当前选择
    if (!nextOpen) {
      reportRecord();
    }
  };

  // 展开组织节点获取子级
  const onLoadOrgData = async (treeNode: any) => {
    const childDeptList = await getDeptHierarchy({
      deptId: treeNode?.value,
      showChildDept: true,
      showChildMi: false
    });

    if (childDeptList) {
      const children = formatOrgData(childDeptList, language, REPORT_SELECT_CHANNEL_ENUM.DEPT);

      setOptions(options.concat(children));
    } else {
      const newOptions = options.map((item) => {
        if (item.value === treeNode?.value) {
          item.isLeaf = true;
          if (item.item) {
            item.item.isLeaf = true;
          }
        }
        return item;
      });
      setOptions(newOptions);
    }
  };

  // 只读
  if (isReadOnly) {
    return <ReadOnly {...props} purpose={purpose} selectValue={selectValue} />;
  }

  const finalProps = {
    placeholder: finalLocale[finalLanguage]?.please_enter,
    ...props,
    style: props?.style?.width
      ? {
          width: `calc(${props?.style?.width} - 32px)`
        }
      : { width: 'calc(100% - 32px)' },
    open: selectOpen,
    showSearch: true,
    suffixIcon: null,
    allowClear,
    maxCount: finalMaxCount,
    maxTagPlaceholder,
    tagRender,
    labelInValue,
    value: selectValue,
    onBlur: handleBlur,
    onClear: handleClear,
    onChange: handleChange,
    onSelect: handleSelect,
    onPopupScroll: handlePopupScroll,
    onDropdownVisibleChange,
    notFoundContent,
    accountKey,
    departmentKey
  };

  return (
    <div className={styles.otakusStaffSelect}>
      {purpose === PURPOSE_ENUM.DEPARTMENT ? (
        <TreeSelect
          {...finalProps}
          treeCheckable={treeCheckable}
          treeCheckStrictly={treeCheckable}
          showCheckedStrategy={SHOW_ALL}
          multiple
          treeTitleRender={optionRender}
          filterTreeNode={false}
          onSearch={debounceFetcher}
          treeDataSimpleMode={{
            id: 'value',
            pId: searchValue || hasRecentSelect ? '' : 'parent_dept_id'
          }}
          treeNodeLabelProp="label"
          loadData={onLoadOrgData}
          treeData={options}
          dropdownStyle={{ maxHeight: 288, overflow: 'auto' }}
          popupClassName={`${styles.otakusStaffTreeSelectDropdown} ${searchValue || hasRecentSelect ? 'is-search' : ''}`}
        />
      ) : (
        <Select
          {...finalProps}
          listItemHeight={48}
          listHeight={288}
          mode="multiple"
          optionRender={optionRender}
          filterOption={false}
          onSearch={debounceFetcher}
          options={options}
          popupClassName={styles.otakusStaffSelectDropdown}
        />
      )}
      <SuffixIcon />
      <PersonSearch
        {...props}
        purpose={purpose}
        value={value}
        open={open}
        onConfirm={handleSelectValueChange}
        onCancel={() => setOpen(false)}
      />
      {/* error Modal */}
      <Modal
        className={styles.staffErrorModal}
        title={
          <>
            <PersonSearchSVG type="info" /> {finalLocale[finalLanguage]?.info}
          </>
        }
        open={errorOpen}
        onOk={() => setErrorOpen(false)}
        width={416}
        centered
        closeIcon={null}
        cancelButtonProps={{ style: { display: 'none' } }}
        okText={finalLocale[finalLanguage]?.known}
        zIndex={2000}
      >
        <span className={styles.staffErrorModalText}>
          {finalLocale[finalLanguage]?.exceed_limit}
        </span>
      </Modal>
    </div>
  );
};

export default PersonSelect;
