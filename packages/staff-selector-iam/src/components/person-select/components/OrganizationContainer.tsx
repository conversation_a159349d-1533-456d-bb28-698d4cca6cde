import React, { useState, useRef, useEffect, useMemo } from 'react';
import { Popover } from '@otakus/design';
import { IOrgItem } from '../../../types';
import { usePrefixCls } from '../../../hooks/usePrefixCls';
import { useStyles } from '../style';
interface IProps {
  label: string;
  value: string;
  avatar_url?: string;
  data: object;
  language: string;
  searchValue: string;
  item: IOrgItem;
  onOpen: (value: boolean) => void;
}
const OrganizationContainer: React.FC<IProps> = (props) => {
  const prefix = usePrefixCls();
  const { styles } = useStyles({ prefixCls: prefix });
  const { label, item, searchValue, language } = props;

  const labelRef = useRef(null);

  const [optionPopover, setOptionPopover] = useState(false);
  const [isCurrent, setIsCurrent] = useState(false);

  useEffect(() => {
    setIsCurrent(true);
    return () => {
      setIsCurrent(false);
    };
  }, []);

  const displayFullPathText = useMemo(() => {
    if (language === 'en-US') {
      return `${item.full_en_path || ''}`;
    }
    if (language === 'zh-CN') {
      return `${item.full_cn_path}`;
    }
  }, [item]);

  // 搜索时匹配完整路径
  const labelContent = !searchValue ? label : displayFullPathText; //highlightKeyword(item, searchValue, 'full_org_path');

  const popoverContent = (
    <div className={styles.otakusStaffSelectOptionPopover}>
      <span className={styles.otakusSelectTreeItemOptionContent}>{labelContent}</span>
    </div>
  );
  const onShowTooltip = () => {
    const labelEl = labelRef.current;
    const isLabelOverflow = labelEl && labelEl.scrollWidth > labelEl.clientWidth;
    return isLabelOverflow ? false : true;
  };

  const onOpenChange = (value) => {
    if (isCurrent && onShowTooltip()) return;
    setOptionPopover(value);
    props?.onOpen(value);
  };

  return (
    <Popover
      content={popoverContent}
      trigger="hover"
      placement="top"
      destroyTooltipOnHide
      zIndex={9999}
      align={{ offset: [0, -4] }}
      onOpenChange={onOpenChange}
      open={optionPopover}
    >
      <div className={`${prefix}-staff-select-tree-node-container`} ref={labelRef}>
        {labelContent}
      </div>
    </Popover>
  );
};

export default OrganizationContainer;
