import React, { useState, useRef, useEffect, useMemo } from 'react';
import {
  // Avatar,
  Popover
} from '@otakus/design';
// import { UserFilled } from '@otakus/icons';
// import PersonSearchSVG from '../../person-search/components/PersonSearchSVG';
import Avatar from '../../person-search/components/Avatar';
import { isResigned } from '../../person-search/utils';
import { useI18n } from '../../person-search/hooks';
import { PURPOSE_ENUM } from '../../../constants/enum';
import { usePrefixCls } from '../../../hooks/usePrefixCls';
import { useStyles } from '../style';

interface IProps {
  label: string;
  value: string;
  avatar_url?: string;
  data: Record<string, any>;
  onOpen: (value: boolean) => void;
  [key: string]: any;
}

const OptionMain = (props: IProps) => {
  const prefix = usePrefixCls();
  const i18n = useI18n();
  const { label, data, language } = props;
  const renderItem = data?.item;
  const isUserResigned = isResigned(renderItem);
  const displayFullPathText = useMemo(() => {
    if (language === 'en-US') {
      return `${renderItem.full_en_path || ''}`;
    }
    if (language === 'zh-CN') {
      return `${renderItem.full_cn_path || ''}`;
    }
  }, [renderItem]);
  return (
    <div className={`${prefix}-select-item-option-main`}>
      <div className={`${prefix}-select-item-option-content`}>
        {/* 组件里有通过otakus-select-item-option-content-text获取dom的操作 */}
        <span className="otakus-select-item-option-content-text">{label}</span>
        {isUserResigned && (
          <div className="staff-item__info-text--status">
            <span>{i18n('deactivate', '停用')}</span>
          </div>
        )}
      </div>
      {/* 组件里有通过otakus-select-item-option-content-organization'获取dom的操作 */}
      <div className="otakus-select-item-option-content-organization">
        {displayFullPathText || ''}
      </div>
    </div>
  );
};
const OptionContainer: React.FC<IProps> = (props) => {
  const prefix = usePrefixCls();
  const { styles } = useStyles({ prefixCls: prefix });
  const { value: optionValue, data, recordPopover, popupScroll } = props;
  const renderItem = data?.item;

  const purposeType = renderItem?.isDept
    ? PURPOSE_ENUM.DEPARTMENT
    : renderItem?.isUser
      ? PURPOSE_ENUM.ACCOUNT
      : '';

  const defaultPopover = recordPopover.get(optionValue)?.isShow ?? false;

  const optionContainerRef = useRef(null);

  const [optionPopover, setOptionPopover] = useState(defaultPopover);

  const hasRecord = useMemo(() => {
    return recordPopover.get(optionValue);
  }, [recordPopover]);

  const avatarUrl = useMemo(() => {
    return renderItem?.avatar_url;
  }, [renderItem]);

  useEffect(() => {
    if (hasRecord) {
      setOptionPopover(hasRecord.isShow);
    }
  }, []);

  useEffect(() => {
    if (!popupScroll && !hasRecord) {
      const optionMainEl = optionContainerRef.current;
      if (optionMainEl) {
        if (!optionMainEl.clientWidth) {
          const resizeObserver = new ResizeObserver((entries) => {
            for (const entry of entries) {
              const { width } = entry.contentRect;
              if (width) {
                onShowTooltip();
                resizeObserver.unobserve(optionMainEl);
              }
            }
          });
          resizeObserver.observe(optionMainEl);
        } else {
          onShowTooltip();
        }
      }
    }
  }, [popupScroll]);

  const onShowTooltip = () => {
    // setTimeout(() => {
    const optionMainEl = optionContainerRef.current;
    if (popupScroll || !optionMainEl) return;
    const labelEl = optionMainEl.querySelector('.otakus-select-item-option-content-text');
    const isLabelOverflow = labelEl && labelEl.scrollWidth > labelEl.clientWidth;
    const organizationEl = optionMainEl.querySelector(
      '.otakus-select-item-option-content-organization'
    );
    const isOrganizationOverflow =
      organizationEl && organizationEl.scrollWidth > organizationEl.clientWidth;
    const showPopover = isLabelOverflow || isOrganizationOverflow ? true : false;
    // console.log(92, hasRecord, showPopover, renderItem);
    // console.log(labelEl, labelEl.scrollWidth, labelEl.clientWidth, labelEl.offsetWidth);
    // console.log(
    //   organizationEl,
    //   organizationEl.scrollWidth,
    //   organizationEl.clientWidth,
    //   organizationEl.offsetWidth
    // );

    if (!hasRecord || hasRecord.isShow !== showPopover) {
      setOptionPopover(showPopover);
      props?.onRecordPopover({
        value: optionValue,
        isShow: showPopover
      });
    }
    // });
  };

  const optionContainer = (
    <div
      className={styles.otakusStaffSelectOptionContainer}
      // onMouseEnter={onShowTooltip}
      ref={optionContainerRef}
    >
      <Avatar
        purpose={purposeType}
        icon={renderItem?.tenant_id === 1 ? 'mihoyoDeptAvatar' : 'hoyoverseDeptAvatar'}
        avatarUrl={avatarUrl}
        styles={{ fontSize: '20px', marginRight: '8px' }}
      ></Avatar>

      <OptionMain {...props} />
    </div>
  );

  const popoverContent = (
    <div className={styles.otakusStaffSelectOptionPopover}>
      <OptionMain {...props} />
    </div>
  );

  const onOpenChange = (open) => {
    props?.onOpen(open);
  };

  return !optionPopover ? (
    optionContainer
  ) : (
    <Popover
      content={popoverContent}
      trigger="hover"
      placement="top"
      destroyTooltipOnHide
      zIndex={9999}
      align={{ offset: [0, -4] }}
      onOpenChange={onOpenChange}
    >
      {optionContainer}
    </Popover>
  );
};

export default OptionContainer;
