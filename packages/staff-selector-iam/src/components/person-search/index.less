// 根组件样式
.staff-search {
  display: flex;
  // padding-top: 20px;
  border: 1px solid var(--otakus-color-split);
  border-radius: var(--otakus-border-radius-lg);
  flex: 1;
  height: 0;

  &__left {
    width: 456px;
    position: relative;
    padding-top: 20px;
    display: flex;
    flex-direction: column;
  }
  &__right {
    width: 456px;
    border-left: 1px solid var(--otakus-color-split, #dbdde2);
    // padding-left: 16px;
    padding-top: 20px;
  }
}

.dept-empty {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-view-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  .more-view-title {
    line-height: 26px;
    padding-left: 8px;
    svg {
      color: var(--otakus-color-icon, --otakus-color-icon);
      cursor: pointer;
      &:hover {
        color: var(--otakus-color-icon-hover, #00030ee0);
      }
    }
  }
  .more-view-content {
    flex: 1;
    height: 0;
    overflow-y: auto;
    margin-right: -12px;

    .staff-chose__checkbox-item--deep {
      width: calc(100% - 12px);
    }
  }
}

// 选中组织后的提示
.staff-selected-dept-tip {
  margin-top: 12px;
  border: 1px solid var(--otakus-color-info-border);
  background: var(--otakus-color-info-bg);
  padding-top: 4px;
  padding-bottom: 4px;
}

// 常用人组样式
.staff-common {
  // width: 440px;
  // padding: 16px 16px 16px 16px;
  border-radius: 8px;
  background-color: var(--otakus-modal-content-bg, #fff);
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  // overflow: hidden;

  &__header {
    color: var(--otakus-color-text-secondary, #586073);
    font-size: 12px;
    padding: 0 12px 0 20px;
    line-height: 28px;
    margin-top: 4px;
  }
  &__list {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    // overflow: auto;
    padding: 0 12px;
    height: 0;

    &-dept {
      margin: 0 8px;
      display: flex;
      flex-direction: column;
      flex-grow: 1;
      height: 100%;
    }

    &-item {
      margin: 8px 8px 0 0;
      padding: 1px 8px;
      border-radius: 4px;
      color: var(--otakus-color-text-label, #586073);
      background: var(--otakus-color-fill-tertiary, #f1f1f6);
      font-size: 12px;
      line-height: 20px;
      cursor: pointer;
      font-weight: 500;
    }
    &-item:hover {
      background: var(--otakus-color-primary-bg-hover, #d6e8ff);
      color: var(--otakus-color-primary-text, #3c47d5);
    }
  }
}

.entry-list-container {
  display: flex;
  flex-direction: column;
  padding: 0 12px;
  .entry-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    cursor: pointer;
    padding: 0 8px;
    &:hover {
      background: var(--otakus-color-bg-text-hover, #f1f1f5);
      border-radius: var(--otakus-border-radius-lg);
    }
  }
  .entry-item-left {
    display: flex;
    align-items: center;
    .item-title {
      margin-left: 8px;
      font-size: 14px;
      letter-spacing: 0.4px;
    }
  }
  .entry-item-right {
    color: var(--otakus-color-icon, #c1c5d0);
  }
}

// 批量添加样式
.batch-list-container {
  height: 100%;
  overflow: auto;
  overflow-x: hidden;
  margin: 0 -20px 0 -8px;
  .batch-item-content {
    .staff-chose__checkbox-item--deep {
      width: 100%;
    }
  }
  .batch-item-container {
    border-bottom: 1px solid var(--otakus-color-split, #dbdde2);
    padding-bottom: 6px;
    margin-right: 12px;
    .batch-item-title {
      display: flex;
      align-items: center;
      justify-content: start;
      height: 28px;
      padding-left: 8px;
      color: var(--otakus-color-text-secondary, #00052a9e);
    }
    .batch-item-empty {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      height: 32px;
      color: var(--otakus-color-text-description);
    }
  }
}
// 组织item的样式
.dept-chose-container {
  display: flex;
  align-items: center;
  min-height: 48px;
  padding-left: 8px;
  margin-right: 12px;
  cursor: pointer;
  &:hover {
    border-radius: var(--otakus-border-radius-lg);
    background: var(--otakus-color-bg-text-hover, #f1f1f5);
  }
  .dept-item-left {
    display: flex;
    align-items: center;
  }
  .dept-item-right {
    padding-right: 8px;
    color: var(--otakus-color-text-description, #02062b6e);
  }
  .dept-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-grow: 1;
    height: 100%;
    .dept-avatar {
      border-radius: var(--otakus-border-radius);
      margin: 0 8px;
      height: 32px;
      overflow: hidden;
      flex-shrink: 0;
      position: relative;

      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        box-shadow: inset 0 0 0 1px var(--otakus-color-split, #dbdde2);
        z-index: 1;
        border-radius: var(--otakus-border-radius);
      }
    }
    &--name {
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 300px;
      overflow: hidden;
    }
    &--path {
      color: var(--otakus-color-text-description);
      font-size: 12px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 300px;
    }
    &__info-text {
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: 100%;
    }
  }
}

.dept-chose-container-disabled {
  cursor: not-allowed;
}

// 组织下钻样式
.dept-container {
  width: calc(100% + 28px);
  overflow: auto;
  margin: 0 0 0 -8px;
}

.dept-checkbox-all {
  line-height: 36px;
  display: flex;
  align-items: center;
}
// 搜索结果tabs样式
.search-tabs {
  .otakus-tabs-content {
    height: 100%;

    .otakus-tabs-tabpane {
      height: 100%;
    }

    // .otakus-tabs-tabpane {
    //   height: 100%;
    //   overflow-y: auto;
    // }
  }
}

.staff-recent-dept-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
}

// 搜索样式
.staff-input {
  // width: 440px;
  position: relative;
  margin: 0 20px 12px;

  &--with-results {
    height: 100%;
  }

  .otakus-input-prefix {
    margin-inline-end: 8px;
    color: var(--otakus-color-text-placeholder, #bec2cb);
    font-size: var(--otakus-font-size-lg, 16px);
  }

  &-container {
    height: calc(100% - 44px);
    left: -20px;
    margin-top: 12px;
    // padding: 0 var(--otakus-margin-sm, 12px);
    background-color: var(--otakus-modal-content-bg, #fff);
    border-radius: var(--otakus-border-radius-lg, 8px);
    // box-shadow: var(--otakus-box-shadow-secondary);
    z-index: 10;
    display: flex;
    flex-direction: column;

    .otakus-tabs-tab {
      margin-left: 0;
      margin-right: 0;
    }

    .otakus-tabs-nav {
      margin-bottom: 0;
    }

    &__list {
      // display: flex;
      // flex-direction: column;
      margin: 0 -20px 0 -8px;
      overflow-y: hidden;
      overflow-x: hidden;
      &.is-all {
        padding-top: 12px;
        overflow: auto;
        height: 100%;
      }

      &-header {
        height: 24px;
        padding: 0 8px 0 4px;
        display: flex;
        align-items: center;
        cursor: pointer;

        &-icon {
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        &-name {
          margin-left: 10px;
        }
      }
      &-checkboxAll {
        height: 40px;
        padding: 8px;
        margin: 8px 0;
      }
      &-body {
        width: 100%;
        flex-direction: column;

        &--virtual {
          height: 38px;
        }

        &.is-recent-select {
          .staff-chose__checkbox-item--deep {
            width: 100%;
          }
          .dept-chose-container {
            margin-right: 0;
          }
        }
      }
      &-head {
        color: var(--otakus-color-text-secondary, #586073);
        line-height: 28px;
        padding: 0 8px;
      }
      &-item {
        padding-left: 8px;
        padding-right: 0;
        display: flex;
        align-items: center;

        &-text {
          white-space: nowrap;
          color: var(--otakus-color-text-heading, #1a1d23);

          &--highlight {
            white-space: nowrap;
            color: var(--otakus-color-primary-text, #3470ff);
          }
        }
      }
      &-item:hover {
        border-radius: 4px;
        background: var(--otakus-color-primary-bg-hover, #d6e8ff);
      }
      &-item-block {
        margin-top: 8px;
        padding-top: 8px;
        display: flex;
        flex-direction: column;
        align-items: baseline;
        position: relative;
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 10px;
          width: calc(100% - 30px);
          height: 1px;
          background-color: var(--otakus-color-split, #f5f5f51c);
        }
      }
      &-item-block:first-child {
        margin-top: 0;
        padding-top: 0;
        &::before {
          display: none;
        }
      }
      &-more {
        color: var(--otakus-color-primary-text, #3470ff);
        padding-left: 8px;
        cursor: pointer;
        line-height: 32px;
      }
      &--empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: var(--otakus-color-text-quaternary, #bec2cb);

        &-text {
          margin-top: 12px;
        }
      }
      &-group {
        display: flex;
        flex-direction: column;
        height: 470px;
        padding: 16px 0;
        overflow-y: auto;
        overflow-x: hidden;

        &--fix {
          display: flex;
          flex-direction: column;
          flex: none;
        }
        &-header {
          height: 24px;
          padding: 0 8px 0 4px;
          display: flex;
          align-items: center;
          cursor: pointer;

          &-icon {
            display: flex;
            align-items: center;
            cursor: pointer;
          }
          &-name {
            margin-left: 10px;
          }
        }
        &-checkboxAll {
          height: 30px;
          padding: 4px 8px;
          margin: 4px 0;
        }
        &-body {
          width: 100%;
          display: flex;
          flex-grow: 1;
          overflow-y: auto;
          flex-direction: column;
          flex-wrap: nowrap;

          &--virtual {
            height: 38px;
          }
        }
      }
    }
  }
  &--loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}

// 群组管理样式
.staff-group {
  display: flex;
  margin-top: 16px;

  &__left {
    display: inline-block;
    width: 456px;

    &-input {
      width: 440px;
      margin-top: 8px;
    }
  }
  &__right {
    display: inline-block;
    width: 456px;
    border-left: 1px dashed #dbdde2;
    padding-left: 16px;
  }
}

// 已选人员列表样式
.staff-selected {
  display: flex;
  flex-direction: column;
  height: 100%;
  &__header {
    display: flex;
    justify-content: space-between;
    height: 32px;
    margin-bottom: 12px;
    padding: 0 20px;

    &-info {
      color: var(--otakus-color-text-description, #828b9d);
      display: flex;
      align-items: center;
    }

    &-number {
      display: inline-block;
      color: var(--otakus-color-text-heading, #00030ee0);
      font-weight: 600;
      margin-left: 4px;

      &--danger {
        cursor: pointer;
        display: inline-block;
        color: var(--otakus-color-error, #dc4947);
        font-weight: 600;
        margin-left: 4px;
      }
    }

    &-text {
      color: var(--otakus-color-text-heading, #00030ee0);
      margin-left: 4px;
      &.is-account-and-dept {
        margin-left: 8px;
      }
    }

    &-operator {
      display: flex;

      &-save,
      &-save--disabled {
        margin-left: 24px;
      }

      &-save,
      &-clear {
        color: var(--otakus-color-error, #dc4947);
        cursor: pointer;
        display: flex;
        align-items: center;
        font-weight: 500;
      }

      &-save:hover,
      &-clear:hover {
        color: var(--otakus-color-error-hover, #fa625a);
      }

      &-save--disabled,
      &-clear--disabled {
        color: var(--otakus-color-text-disabled, #bec2cb);
        cursor: pointer;
        display: flex;
        align-items: center;
      }
    }
  }
  &__body {
    // padding: 0 12px;
    padding-left: 12px;
    overflow-y: auto;
    flex: 1;
    height: 0;

    .staff-item {
      // margin-bottom: 8px;
      padding: 0 8px;
      margin-right: 12px;
      &:hover {
        border-radius: var(--otakus-border-radius-lg);
        background: var(--otakus-color-bg-text-hover, #f1f1f5);
      }
    }

    &-list {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: var(--otakus-color-text-quaternary, #bec2cb);
    }
  }
}

.batch-select-container__list-more {
  display: flex;
  justify-content: start;
  align-items: center;
  height: 32px;
  color: var(--otakus-color-primary-text);
  cursor: pointer;
  padding-left: 8px;
}

// 人员项样式
.staff-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;

  &__info {
    display: flex;
    align-items: center;
    width: 350px;
    height: 100%;

    &-avatar,
    &-custom {
      width: 32px;
      height: 32px;
      border-radius: var(--otakus-border-radius-lg);
      border: 1px solid var(--otakus-color-split, #dbdde2);
      flex-shrink: 0;
    }
    &-custom {
      display: inline-block;
    }
    svg {
      flex-shrink: 0;
    }
    &-dept {
      border-radius: var(--otakus-border-radius);
      height: 32px;
      overflow: hidden;
      flex-shrink: 0;
      position: relative;

      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        box-shadow: inset 0 0 0 1px var(--otakus-color-split, #dbdde2);
        z-index: 1;
        border-radius: var(--otakus-border-radius);
      }
    }

    .otakus-avatar {
      border: none;
      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        box-shadow: inset 0 0 0 1px var(--otakus-color-split, #dbdde2);
        z-index: 1;
        border-radius: var(--otakus-border-radius);
      }
    }
    &-text {
      display: flex;
      margin-left: 8px;
      color: var(--otakus-color-text-heading, #00030ee0);

      flex-direction: column;
      justify-content: center;
      height: 100%;

      .staff-item--dept {
        color: var(--otakus-color-text-description);
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &--name {
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 323px;
      }

      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &--status {
        display: flex;
        border: 1px solid #dbdde2;
        border-radius: 4px;
        padding: 1px 6px;
        margin-left: 8px;
        background-color: #f4f6fa;
        cursor: pointer;

        span {
          font-size: 11px;
          font-weight: 500;
          color: #586073;
        }
      }
    }
  }
  &__close {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--otakus-color-text-description, #02062b6e);
  }
  &__close:hover path {
    fill: var(--otakus-color-primary-hover, #5c92ff);
  }
}

// 人员组织列表样式
.staff-chose {
  // height: 480px;
  // width: 440px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  // overflow: hidden;
  justify-content: center;

  &__list {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    height: 100%;

    &-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24px;
      color: var(--otakus-color-text, #1a1d23);
      cursor: pointer;

      &__left {
        display: flex;
        align-items: center;

        &-icon {
          width: 32px;
          height: 32px;
          padding: 10px;
          border-radius: 4px;
          background: var(--otakus-color-primary-bg-hover, #d6e8ff);
        }
        &-title {
          margin-left: 16px;
        }
      }

      &__right {
        display: flex;
        padding-right: 8px;
      }
    }

    &--empty {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 298px;
      color: var(--otakus-color-text-quaternary, #bec2cb);
    }
    // list下的多选框样式
    .otakus-checkbox-group {
      flex-direction: column;
    }
  }
  &__checkbox {
    width: 100%;

    &-all {
      display: flex;
      flex: none;
      margin: 8px 8px;
      padding: 4px 0;
    }
    &-item,
    &-item--deep {
      // width: 100%;
      width: calc(100% - 12px);
      display: flex;
      cursor: pointer;
      // margin-bottom: 8px;
      padding-left: 8px;
      label {
        flex: 1;
      }
    }
    &-item {
      height: 30px;

      &__group {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 408px;
        margin-left: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &-name {
          display: flex;
          align-items: center;

          &__span {
            max-width: 316px;
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        &--right {
          display: flex;
          align-items: center;
          padding-right: 8px;

          &-edit {
            display: none;
            color: var(--otakus-color-icon, #c1c5d0);
          }
          &-edit:hover {
            color: var(--otakus-color-primary-normal, #3470ff);
          }
          &-next {
            display: flex;
            color: var(--otakus-color-icon, #c1c5d0);
          }
          &-next:hover {
            color: var(--otakus-color-primary-normal, #3470ff);
          }
        }
      }
    }
    &-item--deep {
      height: 48px;

      &-container {
        width: 100%;
      }
      .otakus-checkbox {
        // height: 38px;
        display: flex;
        align-items: center;
      }
    }
    &-item:hover,
    &-item--deep:hover {
      border-radius: var(--otakus-border-radius-lg);
      background: var(--otakus-color-bg-text-hover, #f1f1f5);

      .staff-chose__checkbox-item__group--right-edit {
        display: inline-block;
        margin-right: 16px;
      }
    }
  }
  &__breadcrumb {
    padding: 4px 0;
    flex: none;

    &-item {
      display: inline-block;
      max-width: 292px;
      word-break: break-all;
      cursor: pointer;
    }
    &-item:hover {
      color: var(--otakus-color-primary-text-hover, #5c92ff);
    }
    li {
      max-width: 300px;
    }
  }
  &--loading {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.staff-error-modal {
  svg {
    color: var(--otakus-color-warning, #da8006);
  }
}

.staff-error-modal,
.staff-resigned-modal {
  .otakus-modal-title {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &-text {
    display: block;
    margin-left: 24px;
  }
}

.staff-search-modal {
  .otakus-modal-content {
    min-width: 960px;
    border-radius: 8px;
  }

  * {
    box-sizing: border-box;
  }
}
