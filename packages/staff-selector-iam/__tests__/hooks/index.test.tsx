import { jest, expect, describe, it } from '@jest/globals';
import { useContext } from 'react';
import { useI18n } from '../../src/components/person-search/hooks/index';

jest.mock('react', () => {
  const originalModule = jest.requireActual('react') as typeof import('react');
  return {
    __esModule: true,
    ...originalModule,
    useContext: jest.fn()
  };
});

describe('useI18n', () => {
  it('returns a function that returns localized strings', () => {
    // 设置模拟的 context
    (useContext as jest.Mock).mockImplementation(() => ({
      language: 'zh-CN',
      localeFile: {
        'zh-CN': {
          search_custom: '自定义搜索结果'
        }
      }
    }));

    const translate = useI18n();
    expect(translate('search_custom')).toBe('自定义搜索结果');
  });

  it('returns a function that returns localized strings with empty language and localeFile', () => {
    (useContext as jest.Mock).mockImplementation(() => ({}));

    const translate = useI18n();
    expect(translate('search_custom')).toBe('搜索自定义');
  });

  it('returns the fallback string if the key does not exist', () => {
    (useContext as jest.Mock).mockImplementation(() => ({
      language: 'zh-CN',
      localeFile: {
        'zh-CN': {
          search_custom: '自定义搜索结果'
        }
      }
    }));

    const translate = useI18n();
    expect(translate('nonexistent_key', 'fallback string')).toBe('fallback string');
  });
});
