// isEqualList.test.ts
import { expect, describe, it } from '@jest/globals';
import { isEqualList } from '../../src/utils/isEqualList';
import { PURPOSE_ENUM } from '../../src/constants/enum';
import { IUserItem, IOrgItem } from '../../src/types';

describe('isEqualList', () => {
  // 测试数据准备
  const userItem1: IUserItem = {
    mi_id: 1,
    domain: '<EMAIL>',
    isUser: true
  };

  const userItem2: IUserItem = {
    mi_id: 2,
    domain: '<EMAIL>',
    isUser: true
  };

  const orgItem1: IOrgItem = {
    dept_id: 101,
    org_code: 'ORG1',
    isUser: false
  };

  const orgItem2: IOrgItem = {
    dept_id: 102,
    org_code: 'ORG2',
    isUser: false
  };

  it('should return true for empty arrays', () => {
    expect(isEqualList([], [], PURPOSE_ENUM.ACCOUNT)).toBe(true);
  });

  it('should return false for arrays with different lengths', () => {
    expect(isEqualList([userItem1], [userItem1, userItem2], PURPOSE_ENUM.ACCOUNT)).toBe(false);
  });

  it('should return true for identical user arrays with ACCOUNT purpose', () => {
    const arr1 = [userItem1, userItem2];
    const arr2 = [userItem1, userItem2];
    expect(isEqualList(arr1, arr2, PURPOSE_ENUM.ACCOUNT)).toBe(true);
  });

  it('should return true for identical org arrays with DEPARTMENT purpose', () => {
    const arr1 = [orgItem1, orgItem2];
    const arr2 = [orgItem1, orgItem2];
    expect(isEqualList(arr1, arr2, PURPOSE_ENUM.DEPARTMENT)).toBe(true);
  });

  it('should return true for arrays with same items in different order', () => {
    const arr1 = [userItem1, userItem2];
    const arr2 = [userItem2, userItem1];
    expect(isEqualList(arr1, arr2, PURPOSE_ENUM.ACCOUNT)).toBe(true);
  });

  it('should return false for different arrays with same length', () => {
    const arr1 = [userItem1, userItem2];
    const arr2 = [userItem1, { ...userItem2, mi_id: 3 }];
    expect(isEqualList(arr1, arr2, PURPOSE_ENUM.ACCOUNT)).toBe(false);
  });

  it('should handle HYBRID purpose correctly', () => {
    const arr1 = [userItem1, orgItem1];
    const arr2 = [userItem1, orgItem1];
    expect(isEqualList(arr1, arr2, PURPOSE_ENUM.HYBRID)).toBe(true);
  });

  it('should return false for arrays with different types of items', () => {
    const arr1 = [userItem1, userItem2];
    const arr2 = [userItem1, orgItem1];
    expect(isEqualList(arr1, arr2, PURPOSE_ENUM.HYBRID)).toBe(false);
  });
});
