import { expect, describe, it } from '@jest/globals';
import { isOldUserData, isOldDeptData } from '../../src/utils/oldData';

describe('oldData utils', () => {
  describe('isOldUserData', () => {
    it('should return true for null input', () => {
      expect(isOldUserData(null)).toBe(false);
    });

    it('should return true for undefined input', () => {
      expect(isOldUserData(undefined)).toBe(false);
    });

    it('should return true for old user object without mi_id', () => {
      const oldUser = { name: '<PERSON>', id: 1 };
      expect(isOldUserData(oldUser)).toBe(true);
    });

    it('should return false for new user object with mi_id', () => {
      const newUser = { name: 'John', mi_id: 'MI123' };
      expect(isOldUserData(newUser)).toBe(false);
    });

    it('should return true for array of old user objects', () => {
      const oldUsers = [
        { name: '<PERSON>', id: 1 },
        { name: '<PERSON>', id: 2 }
      ];
      expect(isOldUserData(oldUsers)).toBe(true);
    });

    it('should return false for array containing at least one new user object', () => {
      const mixedUsers = [
        { name: 'John', id: 1 },
        { name: 'Jane', mi_id: 'MI123' }
      ];
      expect(isOldUserData(mixedUsers)).toBe(false);
    });
  });

  describe('isOldDeptData', () => {
    it('should return true for null input', () => {
      expect(isOldDeptData(null)).toBe(false);
    });

    it('should return true for undefined input', () => {
      expect(isOldDeptData(undefined)).toBe(false);
    });

    it('should return true for old department object without dept_id', () => {
      const oldDept = { name: 'IT', id: 1 };
      expect(isOldDeptData(oldDept)).toBe(true);
    });

    it('should return false for new department object with dept_id', () => {
      const newDept = { name: 'IT', dept_id: 'D123' };
      expect(isOldDeptData(newDept)).toBe(false);
    });

    it('should return true for array of old department objects', () => {
      const oldDepts = [
        { name: 'IT', id: 1 },
        { name: 'HR', id: 2 }
      ];
      expect(isOldDeptData(oldDepts)).toBe(true);
    });

    it('should return false for array containing at least one new department object', () => {
      const mixedDepts = [
        { name: 'IT', id: 1 },
        { name: 'HR', dept_id: 'D123' }
      ];
      expect(isOldDeptData(mixedDepts)).toBe(false);
    });
  });
});
