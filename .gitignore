# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
yarn.lock
package-lock.json
# pnpm-lock.yaml

# dist
dist
cjs
es
umd

# misc
.DS_Store

# editor
.vscode
.atom
.idea
.history

# turbo
.turbo

# dumi
**/.dumi/tmp
**/.dumi/tmp-production
.umi/*
.umirc.ts
turbo2.json
.umi/*

coverage/
